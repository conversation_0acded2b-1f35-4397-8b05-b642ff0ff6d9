<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #007bff;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.waiting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .info {
            font-size: 12px;
            color: #6c757d;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
        }
        
        .claim-input {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .claim-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .claim-input button {
            padding: 8px 12px;
            background-color: #17a2b8;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .claim-input button:hover {
            background-color: #138496;
        }
        
        .progress {
            margin: 10px 0;
            font-size: 12px;
            color: #495057;
        }
        
        .spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 5px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 XA Automation</h1>
    </div>
    
    <div id="status" class="status disconnected">
        ⚠️ Not Connected
    </div>
    
    <div id="progress" class="progress" style="display: none;">
        <div class="spinner"></div>
        <span id="progress-text">Working...</span>
    </div>
    
    <div class="controls">
        <button id="connect-btn" class="btn btn-primary">
            🔗 Connect to Web App
        </button>
        
        <div class="claim-input">
            <input type="text" id="claim-input" placeholder="Enter claim number" disabled>
            <button id="search-btn" disabled>🔍 Search</button>
        </div>
        
        <button id="notes-btn" class="btn btn-warning" disabled>
            📝 Go to Notes
        </button>
        
        <button id="pdf-btn" class="btn btn-success" disabled>
            📄 Generate PDF
        </button>
        
        <button id="next-claim-btn" class="btn btn-primary" disabled>
            ⏭️ Process Next Claim
        </button>
    </div>
    
    <div class="info">
        <strong>Instructions:</strong><br>
        1. Make sure the web app is running<br>
        2. Click "Connect to Web App"<br>
        3. Navigate to XactAnalysis<br>
        4. Use the buttons to automate tasks
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
