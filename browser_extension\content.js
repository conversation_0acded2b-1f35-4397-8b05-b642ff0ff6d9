// Content script for XactAnalysis automation
// This script runs on XactAnalysis pages and handles the actual automation

class XactAnalysisAutomator {
    constructor() {
        this.currentClaimNumber = null;
        this.isProcessing = false;
        
        this.init();
    }
    
    init() {
        console.log('XactAnalysis Automator initialized');
        
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async response
        });
        
        // Add visual indicator that extension is active
        this.addExtensionIndicator();
    }
    
    addExtensionIndicator() {
        // Add a small indicator to show the extension is active
        const indicator = document.createElement('div');
        indicator.id = 'xa-automation-indicator';
        indicator.innerHTML = '🤖 XA Automation Active';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(indicator);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 3000);
    }
    
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'searchClaim':
                    const searchResult = await this.searchClaim(request.claimNumber);
                    sendResponse(searchResult);
                    break;
                    
                case 'goToNotes':
                    const notesResult = await this.goToNotes();
                    sendResponse(notesResult);
                    break;
                    
                case 'generatePDF':
                    const pdfResult = await this.generatePDF();
                    sendResponse(pdfResult);
                    break;
                    
                case 'getPageInfo':
                    const pageInfo = this.getPageInfo();
                    sendResponse(pageInfo);
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async searchClaim(claimNumber) {
        console.log(`Searching for claim: ${claimNumber}`);
        this.currentClaimNumber = claimNumber;
        
        try {
            // Common search input selectors for XactAnalysis
            const searchSelectors = [
                'input[placeholder*="search" i]',
                'input[placeholder*="claim" i]',
                'input[name*="search" i]',
                'input[id*="search" i]',
                '#search',
                '.search-input',
                '[data-testid*="search"]',
                'input[type="text"]'
            ];
            
            let searchInput = null;
            
            // Try to find search input
            for (const selector of searchSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    if (element.offsetParent !== null) { // Check if visible
                        searchInput = element;
                        break;
                    }
                }
                if (searchInput) break;
            }
            
            if (!searchInput) {
                throw new Error('Could not find search input field');
            }
            
            // Clear and enter claim number
            searchInput.focus();
            searchInput.value = '';
            searchInput.value = claimNumber;
            
            // Trigger input events
            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
            searchInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            // Wait a moment for any autocomplete
            await this.sleep(1000);
            
            // Try to submit search
            let searchSubmitted = false;
            
            // Method 1: Press Enter
            try {
                searchInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
                searchInput.dispatchEvent(new KeyboardEvent('keypress', { key: 'Enter', bubbles: true }));
                searchInput.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }));
                searchSubmitted = true;
            } catch (e) {
                console.log('Enter key method failed:', e);
            }
            
            // Method 2: Look for search button
            if (!searchSubmitted) {
                const searchButtons = [
                    'button[type="submit"]',
                    'button:has-text("Search")',
                    'button:has-text("Find")',
                    'input[type="submit"]',
                    '.search-button',
                    '[data-testid*="search"]'
                ];
                
                for (const selector of searchButtons) {
                    const button = document.querySelector(selector);
                    if (button && button.offsetParent !== null) {
                        button.click();
                        searchSubmitted = true;
                        break;
                    }
                }
            }
            
            if (!searchSubmitted) {
                console.log('Could not submit search, but claim number entered');
            }
            
            // Wait longer for search results to load
            await this.sleep(3000);

            // Look for the claim in results with retries
            const claimFound = await this.findAndSelectClaimWithRetries(claimNumber, 3);
            
            return {
                success: claimFound,
                message: claimFound ? `Found claim ${claimNumber}` : `Claim ${claimNumber} not found`,
                claimNumber: claimNumber
            };
            
        } catch (error) {
            console.error('Search error:', error);
            return {
                success: false,
                error: error.message,
                claimNumber: claimNumber
            };
        }
    }
    
    async findAndSelectClaimWithRetries(claimNumber, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            console.log(`Attempt ${attempt}/${maxRetries} to find claim ${claimNumber}`);

            // Debug: Log what we can see on the page
            if (attempt === 1) {
                this.debugPageContent(claimNumber);
            }

            const found = await this.findAndSelectClaim(claimNumber);
            if (found) {
                return true;
            }

            // Wait before retry (except on last attempt)
            if (attempt < maxRetries) {
                console.log(`Claim not found, waiting 3 seconds before retry...`);
                await this.sleep(3000);
            }
        }

        return false;
    }

    debugPageContent(claimNumber) {
        console.log('=== DEBUG: Page Content Analysis ===');
        console.log('Current URL:', window.location.href);
        console.log('Page Title:', document.title);

        // Log all links on the page
        const links = document.querySelectorAll('a');
        console.log(`Found ${links.length} links on page`);

        // Log any text that contains the claim number
        const allText = document.querySelectorAll('*');
        const matchingElements = [];

        for (const element of allText) {
            if (element.textContent && element.textContent.includes(claimNumber)) {
                matchingElements.push({
                    tag: element.tagName,
                    text: element.textContent.trim(),
                    isLink: element.tagName === 'A',
                    hasParentLink: !!element.closest('a'),
                    hasChildLink: !!element.querySelector('a')
                });
            }
        }

        console.log(`Found ${matchingElements.length} elements containing "${claimNumber}":`, matchingElements);
        console.log('=== END DEBUG ===');
    }

    async findAndSelectClaim(claimNumber) {
        console.log(`Looking for clickable claim: ${claimNumber}`);

        // Strategy 1: Look for direct links containing the claim number
        const links = document.querySelectorAll('a');
        for (const link of links) {
            if (link.textContent && link.textContent.trim() === claimNumber) {
                console.log(`Found direct link for claim: ${claimNumber}`);
                link.click();
                await this.sleep(4000); // Wait longer for page navigation

                const isDetailPage = this.isOnClaimDetailPage();
                console.log(`Clicked claim link, on detail page: ${isDetailPage}`);
                if (isDetailPage) return true;
            }
        }

        // Strategy 2: Look in table cells and find associated links
        const allElements = document.querySelectorAll('td, div, span');

        for (const element of allElements) {
            if (element.textContent && element.textContent.trim() === claimNumber) {
                console.log(`Found claim ${claimNumber} in ${element.tagName}`);

                let clickTarget = null;

                // Look for a link in the same table row
                const parentRow = element.closest('tr');
                if (parentRow) {
                    // Find any link in this row
                    const rowLink = parentRow.querySelector('a');
                    if (rowLink) {
                        clickTarget = rowLink;
                        console.log(`Found row link: ${rowLink.href || rowLink.textContent.trim()}`);
                    }
                }

                // Look for a parent link
                if (!clickTarget) {
                    const parentLink = element.closest('a');
                    if (parentLink) {
                        clickTarget = parentLink;
                        console.log(`Found parent link: ${parentLink.href || parentLink.textContent.trim()}`);
                    }
                }

                // Look for a child link
                if (!clickTarget) {
                    const childLink = element.querySelector('a');
                    if (childLink) {
                        clickTarget = childLink;
                        console.log(`Found child link: ${childLink.href || childLink.textContent.trim()}`);
                    }
                }

                // Try clicking if we found a target
                if (clickTarget) {
                    try {
                        console.log(`Clicking on: ${clickTarget.tagName} - ${clickTarget.href || clickTarget.textContent.trim()}`);
                        clickTarget.click();
                        await this.sleep(4000); // Wait for page navigation

                        const isDetailPage = this.isOnClaimDetailPage();
                        console.log(`Clicked claim element, on detail page: ${isDetailPage}`);
                        if (isDetailPage) return true;

                    } catch (e) {
                        console.log('Failed to click element:', e);
                    }
                }
            }
        }

        console.log(`Could not find clickable element for claim: ${claimNumber}`);
        return false;
    }

    isOnClaimDetailPage() {
        // Check if we're on a claim detail page by looking for common indicators
        const indicators = [
            // URL contains claim number or detail indicators
            () => window.location.href.includes('detail'),
            () => window.location.href.includes('claim'),
            () => window.location.href.includes('view'),

            // Page contains claim-specific elements
            () => document.querySelector('[class*="claim"]'),
            () => document.querySelector('[id*="claim"]'),
            () => document.querySelector('[class*="detail"]'),
            () => document.querySelector('[id*="detail"]'),

            // Look for Notes tab/section (common on detail pages)
            () => document.querySelector('a[href*="notes"], button:contains("Notes"), [data-tab="notes"]'),

            // Look for typical claim detail content
            () => document.querySelector('table[class*="detail"], div[class*="detail"]'),
            () => document.querySelector('.notes, #notes, [class*="note"]')
        ];

        // If any indicator is true, we're likely on a detail page
        for (const check of indicators) {
            try {
                if (check()) {
                    console.log('Detail page indicator found');
                    return true;
                }
            } catch (e) {
                // Ignore errors in checks
            }
        }

        console.log('No detail page indicators found');
        return false;
    }

    async goToNotes() {
        console.log('Navigating to Notes section');
        
        try {
            // Common selectors for Notes tab/link
            const notesSelectors = [
                'a[href*="notes" i]',
                'button:contains("Notes")',
                'a:contains("Notes")',
                'tab:contains("Notes")',
                '[data-tab="notes"]',
                '[data-testid*="notes"]',
                '.notes-tab',
                '#notes-tab',
                'li:contains("Notes")',
                '[title="Notes"]'
            ];
            
            let notesElement = null;
            
            // Try to find Notes element
            for (const selector of notesSelectors) {
                try {
                    if (selector.includes(':contains(')) {
                        // Handle text-based selectors
                        const elements = document.querySelectorAll('a, button, tab, li, div, span');
                        for (const element of elements) {
                            if (element.textContent && element.textContent.toLowerCase().includes('notes')) {
                                if (element.offsetParent !== null) { // Check if visible
                                    notesElement = element;
                                    break;
                                }
                            }
                        }
                    } else {
                        const element = document.querySelector(selector);
                        if (element && element.offsetParent !== null) {
                            notesElement = element;
                            break;
                        }
                    }
                } catch (e) {
                    console.log(`Selector failed: ${selector}`, e);
                }
                
                if (notesElement) break;
            }
            
            if (!notesElement) {
                throw new Error('Could not find Notes tab/link');
            }
            
            // Click the Notes element
            notesElement.click();
            
            // Wait for navigation
            await this.sleep(2000);
            
            console.log('Successfully navigated to Notes');
            return {
                success: true,
                message: 'Successfully navigated to Notes section'
            };
            
        } catch (error) {
            console.error('Notes navigation error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async generatePDF() {
        console.log('Generating PDF from current page');
        
        try {
            // Trigger browser's print dialog which can save as PDF
            window.print();
            
            // Wait a moment
            await this.sleep(1000);
            
            return {
                success: true,
                message: 'PDF print dialog opened',
                data: {
                    claimNumber: this.currentClaimNumber,
                    url: window.location.href,
                    title: document.title,
                    timestamp: new Date().toISOString()
                }
            };
            
        } catch (error) {
            console.error('PDF generation error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    getPageInfo() {
        return {
            success: true,
            data: {
                url: window.location.href,
                title: document.title,
                currentClaim: this.currentClaimNumber,
                isXactAnalysis: window.location.hostname.includes('xactanalysis')
            }
        };
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the automator when the page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new XactAnalysisAutomator();
    });
} else {
    new XactAnalysisAutomator();
}
