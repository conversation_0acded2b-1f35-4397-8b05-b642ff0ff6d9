# Chrome Debug Port Troubleshooting Guide

## 🚀 Quick Solutions

### Method 1: Use the Automated Launchers
1. **Windows Batch File**: Double-click `launch_chrome_debug.bat`
2. **PowerShell Script**: Right-click `launch_chrome_debug.ps1` → "Run with PowerShell"

### Method 2: Manual Command Line
1. Close all Chrome windows
2. Open Command Prompt as **Administrator**
3. Run one of these commands:

```batch
# Basic command
chrome.exe --remote-debugging-port=9222

# With temporary profile (recommended)
chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug"

# Full command with all flags
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug" --disable-web-security --no-first-run
```

## 🔧 Common Issues & Solutions

### Issue 1: "Port 9222 already in use"
**Solutions:**
- Close all Chrome instances: `taskkill /f /im chrome.exe`
- Use a different port: `--remote-debugging-port=9223`
- Check what's using the port: `netstat -ano | findstr :9222`

### Issue 2: "Chrome not found"
**Check these paths:**
- `C:\Program Files\Google\Chrome\Application\chrome.exe`
- `C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`
- `%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe`

**Solution:** Update the path in the launcher scripts or install Chrome.

### Issue 3: "Connection refused" in web interface
**Solutions:**
1. Wait 10-15 seconds after starting Chrome
2. Check if Chrome actually started with debug port:
   - Open: http://localhost:9222/json/version
   - Should show Chrome version info
3. Try different port numbers (9223, 9224, etc.)
4. Restart Chrome with debug flags

### Issue 4: "Access denied" or permission errors
**Solutions:**
- Run Command Prompt as **Administrator**
- Run PowerShell as **Administrator**
- Check Windows Firewall settings
- Temporarily disable antivirus

### Issue 5: Chrome starts but automation can't connect
**Solutions:**
1. Verify debug port is working:
   ```
   curl http://localhost:9222/json/version
   ```
2. Check Chrome was started with the right flags
3. Try restarting Chrome completely
4. Use a fresh user profile: `--user-data-dir="%TEMP%\chrome_debug_new"`

## 🔍 Verification Steps

### 1. Check if Chrome Debug Port is Working
Open in any browser: `http://localhost:9222`
- Should show a list of open tabs
- If you see JSON data, the debug port is working!

### 2. Test with curl (if available)
```bash
curl http://localhost:9222/json/version
```

### 3. Check Chrome Process
```batch
tasklist | findstr chrome.exe
```
Should show Chrome processes running.

## 🛠️ Advanced Troubleshooting

### Check Network Connections
```batch
netstat -ano | findstr :9222
```

### Kill Specific Process
```batch
# Find the PID from netstat, then:
taskkill /f /pid [PID_NUMBER]
```

### Chrome Flags Explanation
- `--remote-debugging-port=9222`: Enables debug interface
- `--user-data-dir=TEMP_DIR`: Uses temporary profile
- `--disable-web-security`: Allows automation to work
- `--no-first-run`: Skips Chrome setup wizard

## 📞 Still Having Issues?

### Try These Alternative Ports:
- 9223, 9224, 9225, 9226

### Alternative Chrome Channels:
- Chrome Beta
- Chrome Dev
- Chrome Canary

### Last Resort:
1. Completely uninstall and reinstall Chrome
2. Run Windows as Administrator
3. Temporarily disable Windows Firewall
4. Check if corporate policies are blocking debug ports

## ✅ Success Indicators

You know it's working when:
1. Chrome opens with a yellow "Chrome is being controlled by automated test software" banner
2. http://localhost:9222 shows tab information
3. The web interface shows "Connected" status
4. You can see the current page URL in the connection info

## 🔗 Useful Links

- Chrome DevTools Protocol: https://chromedevtools.github.io/devtools-protocol/
- Chrome Command Line Switches: https://peter.sh/experiments/chromium-command-line-switches/
