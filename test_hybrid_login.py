#!/usr/bin/env python3
"""
Test script for the hybrid login approach.

This script tests the Selenium + Beautiful Soup hybrid system.
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))


def test_hybrid_login():
    """Test the hybrid login system."""
    print("🔬 Testing Hybrid Login System")
    print("=" * 50)
    
    try:
        # Check if Selenium is available
        try:
            from selenium import webdriver
            print("✅ Selenium is available")
        except ImportError:
            print("❌ Selenium not installed. Run: pip install selenium")
            return False
        
        # Check if Chrome is available
        try:
            from selenium.webdriver.chrome.options import Options
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=options)
            driver.get('https://www.google.com')
            driver.quit()
            print("✅ Chrome WebDriver is working")
        except Exception as e:
            print(f"❌ Chrome WebDriver issue: {e}")
            print("💡 Make sure Chrome and ChromeDriver are installed")
            return False
        
        # Test hybrid session
        from src.automation_bs4.hybrid_session import HybridSession
        
        print("\n🔐 Testing hybrid session creation...")
        session = HybridSession()
        print("✅ Hybrid session created")
        
        # Test credentials
        try:
            username, password = session.get_credentials()
            print(f"✅ Credentials found: {username}")
        except Exception as e:
            print(f"❌ Credential error: {e}")
            return False
        
        # Test login (this will actually try to login)
        print("\n🚀 Testing actual login...")
        print("⚠️  This will open a headless browser and attempt login...")
        
        try:
            success = session.login()
            if success:
                print("✅ Hybrid login successful!")
                print("✅ Session cookies transferred to requests")
                
                # Test a simple request
                try:
                    response = session.get('https://www.xactanalysis.com')
                    print(f"✅ Authenticated request successful: {response.status_code}")
                except Exception as e:
                    print(f"⚠️  Authenticated request failed: {e}")
                
            else:
                print("❌ Hybrid login failed")
                return False
                
        except Exception as e:
            print(f"❌ Login test failed: {e}")
            return False
        finally:
            session.close()
        
        print("\n🎉 Hybrid login test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    try:
        success = test_hybrid_login()
        if success:
            print("\n✅ Hybrid system is ready!")
            print("💡 You can now use the hybrid approach for XactAnalysis automation")
        else:
            print("\n❌ Hybrid system test failed")
            print("💡 Check the error messages above and fix any issues")
        return 0 if success else 1
    except Exception as e:
        print(f"\n❌ Test script failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
