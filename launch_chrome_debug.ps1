# PowerShell script to launch Chrome with debug port for XactAnalysis automation

Write-Host "Starting Chrome with Debug Port for XactAnalysis Automation" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green
Write-Host ""

# Function to find Chrome installation
function Find-Chrome {
    $chromePaths = @(
        "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe",
        "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe",
        "${env:LOCALAPPDATA}\Google\Chrome\Application\chrome.exe"
    )
    
    foreach ($path in $chromePaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    return $null
}

# Function to check if port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Close existing Chrome instances
Write-Host "Closing existing Chrome instances..." -ForegroundColor Yellow
Get-Process -Name "chrome" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# Check if port 9222 is already in use
if (Test-Port -Port 9222) {
    Write-Host "⚠️  Port 9222 is already in use!" -ForegroundColor Red
    Write-Host "Trying to find what's using it..." -ForegroundColor Yellow
    
    try {
        $processInfo = netstat -ano | Select-String ":9222"
        if ($processInfo) {
            Write-Host "Port 9222 usage:" -ForegroundColor Yellow
            Write-Host $processInfo -ForegroundColor White
        }
    }
    catch {
        Write-Host "Could not determine what's using port 9222" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "1. Kill the process using port 9222" -ForegroundColor White
    Write-Host "2. Use a different port (like 9223)" -ForegroundColor White
    Write-Host "3. Continue anyway (might work if it's an old Chrome instance)" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Enter choice (1/2/3) or press Enter to continue"
    
    switch ($choice) {
        "1" {
            Write-Host "Attempting to free port 9222..." -ForegroundColor Yellow
            # Try to kill processes using port 9222
            try {
                $netstatOutput = netstat -ano | Select-String ":9222"
                if ($netstatOutput) {
                    $pid = ($netstatOutput -split '\s+')[-1]
                    Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
                    Write-Host "Killed process $pid" -ForegroundColor Green
                    Start-Sleep -Seconds 1
                }
            }
            catch {
                Write-Host "Could not automatically kill the process" -ForegroundColor Red
            }
        }
        "2" {
            $debugPort = 9223
            Write-Host "Using port $debugPort instead" -ForegroundColor Green
        }
        default {
            $debugPort = 9222
            Write-Host "Continuing with port 9222..." -ForegroundColor Yellow
        }
    }
}
else {
    $debugPort = 9222
}

# Find Chrome installation
$chromePath = Find-Chrome

if (-not $chromePath) {
    Write-Host "❌ Chrome not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Chrome or check these paths:" -ForegroundColor Yellow
    Write-Host "- ${env:ProgramFiles}\Google\Chrome\Application\chrome.exe" -ForegroundColor White
    Write-Host "- ${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe" -ForegroundColor White
    Write-Host "- ${env:LOCALAPPDATA}\Google\Chrome\Application\chrome.exe" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Found Chrome at: $chromePath" -ForegroundColor Green

# Create temporary profile directory
$tempProfile = Join-Path $env:TEMP "chrome_debug_profile_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
if (Test-Path $tempProfile) {
    Remove-Item $tempProfile -Recurse -Force
}
New-Item -ItemType Directory -Path $tempProfile -Force | Out-Null

Write-Host "📁 Using temporary profile: $tempProfile" -ForegroundColor Cyan
Write-Host ""

# Chrome arguments
$chromeArgs = @(
    "--remote-debugging-port=$debugPort",
    "--user-data-dir=`"$tempProfile`"",
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor",
    "--disable-blink-features=AutomationControlled",
    "--no-first-run",
    "--no-default-browser-check",
    "--disable-default-apps",
    "--disable-popup-blocking",
    "--disable-extensions",
    "https://www.xactanalysis.com"
)

Write-Host "🚀 Starting Chrome with debug port $debugPort..." -ForegroundColor Green
Write-Host ""
Write-Host "Chrome will open with:" -ForegroundColor Cyan
Write-Host "- Remote debugging on port $debugPort" -ForegroundColor White
Write-Host "- Temporary profile (clean session)" -ForegroundColor White
Write-Host "- Web security disabled for automation" -ForegroundColor White
Write-Host "- Direct navigation to XactAnalysis" -ForegroundColor White
Write-Host ""

# Launch Chrome
try {
    $process = Start-Process -FilePath $chromePath -ArgumentList $chromeArgs -PassThru
    Write-Host "✅ Chrome started with PID: $($process.Id)" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to start Chrome: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Wait a moment and test the debug port
Write-Host "⏳ Waiting for Chrome to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Test debug port
Write-Host "🔍 Testing debug port connection..." -ForegroundColor Cyan
$maxAttempts = 10
$attempt = 0

do {
    $attempt++
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$debugPort/json/version" -TimeoutSec 2 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Debug port $debugPort is responding!" -ForegroundColor Green
            $versionInfo = $response.Content | ConvertFrom-Json
            Write-Host "Chrome Version: $($versionInfo.Browser)" -ForegroundColor White
            break
        }
    }
    catch {
        if ($attempt -lt $maxAttempts) {
            Write-Host "⏳ Attempt $attempt/$maxAttempts - waiting for debug port..." -ForegroundColor Yellow
            Start-Sleep -Seconds 2
        }
        else {
            Write-Host "⚠️  Debug port not responding after $maxAttempts attempts" -ForegroundColor Red
            Write-Host "Chrome might still be starting up - try connecting anyway" -ForegroundColor Yellow
        }
    }
} while ($attempt -lt $maxAttempts)

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Green
Write-Host "1. Wait for Chrome to fully load XactAnalysis" -ForegroundColor White
Write-Host "2. Log into your XactAnalysis account" -ForegroundColor White
Write-Host "3. Go to the automation web interface" -ForegroundColor White
Write-Host "4. Enter port number: $debugPort" -ForegroundColor Yellow
Write-Host "5. Click 'Connect to Browser'" -ForegroundColor White
Write-Host ""

Write-Host "🔧 Troubleshooting:" -ForegroundColor Cyan
Write-Host "- If connection fails, wait a few more seconds" -ForegroundColor White
Write-Host "- Try running this script as Administrator" -ForegroundColor White
Write-Host "- Check Windows Firewall settings" -ForegroundColor White
Write-Host "- Make sure no antivirus is blocking the connection" -ForegroundColor White
Write-Host ""

Write-Host "Debug port: $debugPort" -ForegroundColor Yellow
Write-Host "Chrome PID: $($process.Id)" -ForegroundColor Yellow
Write-Host ""

Read-Host "Press Enter to close this window"
