#!/usr/bin/env python3
"""
Quick start demonstration for XactAnalysis Beautiful Soup Automation.

This script demonstrates the automated system with test credentials.
"""

import os
import sys
import webbrowser
import threading
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))


def print_banner():
    """Print quick start banner."""
    print("=" * 70)
    print("🚀 XactAnalysis Automated Processor - Quick Start Demo")
    print("=" * 70)
    print()
    print("✨ This demo uses test credentials and sample data")
    print("📧 Test Account: <EMAIL>")
    print("🔑 Password: Nubilt2025!")
    print("📄 Sample CSV: sample_claims.csv")
    print()


def check_setup():
    """Check if system is ready."""
    print("🔍 Checking system setup...")
    
    # Check if .env file exists
    env_file = Path('.env')
    if not env_file.exists():
        print("   ⚠️  .env file not found, creating with test credentials...")
        with open(env_file, 'w') as f:
            f.write("XACT_USERNAME=<EMAIL>\n")
            f.write("XACT_PASSWORD=Nubilt2025!\n")
        print("   ✅ Created .env file with test credentials")
    else:
        print("   ✅ .env file exists")
    
    # Check sample CSV
    sample_csv = Path('sample_claims.csv')
    if sample_csv.exists():
        print("   ✅ Sample CSV file found")
    else:
        print("   ⚠️  Sample CSV not found, creating...")
        with open(sample_csv, 'w') as f:
            f.write("Status,Claim Number,Description\n")
            f.write(",********,Test claim for automation\n")
            f.write(",87654321,Sample claim number\n")
            f.write(",11111111,Another test claim\n")
        print("   ✅ Created sample CSV file")
    
    # Check dependencies
    try:
        import requests
        import bs4
        import pandas
        import flask
        print("   ✅ All dependencies available")
    except ImportError as e:
        print(f"   ❌ Missing dependency: {e}")
        print("   💡 Run: pip install -r requirements.txt")
        return False
    
    return True


def test_login():
    """Test login functionality."""
    print("🔐 Testing login functionality...")
    
    try:
        from src.automation_bs4.xact_session import XactSession
        
        session = XactSession()
        print("   ✅ Session created")
        
        # Test credentials
        try:
            username, password = session.get_credentials()
            print(f"   ✅ Credentials loaded: {username}")
        except Exception as e:
            print(f"   ❌ Credential error: {e}")
            return False
        
        session.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Login test failed: {e}")
        return False


def open_browser_delayed(url, delay=3):
    """Open browser after a delay."""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 Opened browser: {url}")
    except Exception as e:
        print(f"⚠️  Could not open browser: {e}")


def print_instructions():
    """Print usage instructions."""
    print("📋 Quick Start Instructions:")
    print()
    print("1. 🌐 Web interface will open automatically")
    print("2. 📤 Upload the sample CSV file (sample_claims.csv)")
    print("3. 🔑 Click 'Login to XactAnalysis' (uses test credentials)")
    print("4. ⚡ Click 'Process Next Claim' or 'Process All Claims'")
    print("5. 📁 Check the 'output' folder for generated PDFs")
    print()
    print("🎯 The system will:")
    print("   • Automatically log into XactAnalysis")
    print("   • Search for claims with 3-year date range")
    print("   • Navigate to notes sections")
    print("   • Generate and save PDFs")
    print("   • Mark completed claims with 'D' in CSV")
    print()


def main():
    """Main quick start function."""
    print_banner()
    
    # Check setup
    if not check_setup():
        print("❌ Setup check failed. Please fix issues and try again.")
        return 1
    
    # Test login
    if not test_login():
        print("❌ Login test failed. Please check credentials.")
        return 1
    
    print("✅ System ready!")
    print()
    
    print_instructions()
    
    try:
        # Import and start the web app
        from src.automation_bs4.web_app import app
        
        host = '127.0.0.1'
        port = 5001
        url = f"http://{host}:{port}"
        
        print(f"🚀 Starting web application at {url}")
        print("Press Ctrl+C to stop")
        print("=" * 70)
        
        # Open browser in background
        browser_thread = threading.Thread(
            target=open_browser_delayed,
            args=(url,),
            daemon=True
        )
        browser_thread.start()
        
        # Start Flask app
        app.run(
            debug=False,
            host=host,
            port=port,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
