"""
Hybrid automation controller using Selenium for login and Beautiful Soup for processing.

This controller provides the best of both worlds: reliable JavaScript login
with fast Beautiful Soup claim processing.
"""

from typing import Optional, Dict, Any

from .hybrid_session import HybridSession
from .claim_navigator import ClaimNavigator
from .pdf_extractor import PDFExtractor
from .csv_processor import CSVProcessor
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import LoginFailedException


class HybridAutomationController:
    """Hybrid automation controller for XactAnalysis."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the hybrid automation controller.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Initialize components with hybrid session
        self.session = HybridSession(self.config)
        self.navigator = ClaimNavigator(self.session, self.config)
        self.pdf_extractor = PDFExtractor(self.session, self.config)
        self.csv_processor = CSVProcessor(self.config)
        
        # State
        self.is_logged_in = False
        self.current_csv_file = None
        
        self.logger.info("Hybrid automation controller initialized")
    
    def load_csv_file(self, file_path: str) -> Dict[str, Any]:
        """
        Load a CSV file for processing.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Dictionary with load results and file summary
        """
        try:
            self.logger.info(f"Loading CSV file: {file_path}")
            
            # Load the CSV file
            success = self.csv_processor.load_csv(file_path)
            
            if success:
                self.current_csv_file = file_path
                
                # Validate CSV format
                is_valid, issues = self.csv_processor.validate_csv_format()
                
                # Get file summary
                summary = self.csv_processor.get_file_summary()
                
                result = {
                    'success': True,
                    'message': 'CSV file loaded successfully',
                    'file_path': file_path,
                    'is_valid': is_valid,
                    'validation_issues': issues,
                    'summary': summary
                }
                
                if not is_valid:
                    result['message'] += f" (with {len(issues)} validation issues)"
                
                return result
            else:
                return {
                    'success': False,
                    'message': 'Failed to load CSV file',
                    'file_path': file_path
                }
                
        except Exception as e:
            self.logger.error(f"Failed to load CSV file: {e}")
            return {
                'success': False,
                'message': f'Failed to load CSV file: {str(e)}',
                'file_path': file_path
            }
    
    def login(self) -> Dict[str, Any]:
        """
        Login to XactAnalysis using hybrid approach.
        
        Returns:
            Dictionary with login results
        """
        try:
            self.logger.info("Attempting hybrid login to XactAnalysis")
            
            success = self.session.login()
            
            if success:
                self.is_logged_in = True
                return {
                    'success': True,
                    'message': 'Successfully logged into XactAnalysis using hybrid method'
                }
            else:
                return {
                    'success': False,
                    'message': 'Hybrid login failed'
                }
                
        except Exception as e:
            self.logger.error(f"Hybrid login failed: {e}")
            return {
                'success': False,
                'message': f'Hybrid login failed: {str(e)}'
            }
    
    def process_single_claim(self, claim_number: str) -> Dict[str, Any]:
        """
        Process a single claim using Beautiful Soup after hybrid login.
        
        Args:
            claim_number: Claim number to process
            
        Returns:
            Dictionary with processing results
        """
        if not self.is_logged_in:
            return {
                'success': False,
                'message': 'Not logged in. Please login first.',
                'claim_number': claim_number
            }
        
        try:
            self.logger.info(f"Processing claim: {claim_number}")
            
            # Search for the claim using Beautiful Soup
            claims = self.navigator.search_claim(claim_number)
            
            if not claims:
                return {
                    'success': False,
                    'message': f'Claim {claim_number} not found',
                    'claim_number': claim_number
                }
            
            # Process each claim result
            processed_pdfs = []
            
            for i, claim_data in enumerate(claims):
                try:
                    # Navigate to notes
                    notes_url = self.navigator.navigate_to_notes(claim_data)
                    
                    # Extract PDF
                    pdf_path = self.pdf_extractor.extract_notes_pdf(claim_number, notes_url, i)
                    
                    processed_pdfs.append({
                        'pdf_path': pdf_path,
                        'suffix_index': i,
                        'notes_url': notes_url
                    })
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process claim variant {i} for {claim_number}: {e}")
                    continue
            
            if not processed_pdfs:
                return {
                    'success': False,
                    'message': f'Failed to extract any PDFs for claim {claim_number}',
                    'claim_number': claim_number
                }
            
            # Mark claim as completed in CSV if loaded
            if self.current_csv_file:
                self.csv_processor.mark_claim_completed(claim_number)
                self.csv_processor.save_csv()
            
            return {
                'success': True,
                'message': f'Successfully processed claim {claim_number}',
                'claim_number': claim_number,
                'pdfs_generated': len(processed_pdfs),
                'pdf_paths': [pdf['pdf_path'] for pdf in processed_pdfs]
            }
            
        except Exception as e:
            self.logger.error(f"Failed to process claim {claim_number}: {e}")
            return {
                'success': False,
                'message': f'Failed to process claim {claim_number}: {str(e)}',
                'claim_number': claim_number
            }
    
    def process_next_claim(self) -> Dict[str, Any]:
        """
        Process the next pending claim from the CSV file.
        
        Returns:
            Dictionary with processing results
        """
        if not self.current_csv_file:
            return {
                'success': False,
                'message': 'No CSV file loaded'
            }
        
        # Get next pending claim
        next_claim = self.csv_processor.get_next_pending_claim()
        
        if not next_claim:
            return {
                'success': True,
                'message': 'No pending claims to process',
                'completed': True
            }
        
        # Process the claim
        result = self.process_single_claim(next_claim['claim_number'])
        
        # Add claim info to result
        result['claim_info'] = next_claim
        
        return result
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the hybrid automation system.
        
        Returns:
            Dictionary with current status
        """
        status = {
            'is_logged_in': self.is_logged_in,
            'current_csv_file': self.current_csv_file,
            'csv_summary': None,
            'method': 'hybrid'
        }
        
        if self.current_csv_file:
            status['csv_summary'] = self.csv_processor.get_file_summary()
        
        return status
    
    def cleanup(self):
        """Clean up resources and close connections."""
        try:
            if self.session:
                self.session.close()
            self.is_logged_in = False
            self.logger.info("Hybrid automation controller cleaned up")
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")
