#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to inspect the Excel file structure and understand the data layout.
"""

import pandas as pd
from pathlib import Path

def main():
    """Inspect the Excel file to understand its structure."""
    excel_path = Path("Adam Example Info/Adam Jobs in 24.xlsx")
    
    if not excel_path.exists():
        print(f"Excel file not found: {excel_path}")
        return 1
    
    print(f"Inspecting Excel file: {excel_path}")
    print("=" * 60)
    
    # Read the Excel file
    df = pd.read_excel(excel_path, sheet_name=0)
    
    print(f"Shape: {df.shape} (rows x columns)")
    print(f"Columns: {list(df.columns)}")
    print()
    
    # Show column headers with indices
    print("Column indices and headers:")
    for i, col in enumerate(df.columns):
        print(f"  {i}: {col}")
    print()
    
    # Show first few rows
    print("First 10 rows:")
    print(df.head(10).to_string())
    print()
    
    # Look for claim number patterns
    print("Looking for claim number patterns in each column:")
    for i, col in enumerate(df.columns):
        print(f"\nColumn {i} ({col}):")
        sample_values = df[col].dropna().head(5).tolist()
        for j, val in enumerate(sample_values):
            print(f"  Row {j+1}: {val}")
    
    return 0

if __name__ == "__main__":
    main()
