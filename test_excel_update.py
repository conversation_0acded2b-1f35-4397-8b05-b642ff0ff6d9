#!/usr/bin/env python3
"""
Test script for Excel update functionality.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_manager import ConfigManager
from src.data.excel_processor import ExcelProcessor

def main():
    """Test the Excel update functionality."""
    try:
        print("Testing Excel Update Functionality...")
        
        # Initialize
        config = ConfigManager()
        processor = ExcelProcessor(config)
        
        # Get first pending claim
        print("\n1. Getting pending claims...")
        pending_claims = processor.get_pending_claims()
        
        if not pending_claims:
            print("No pending claims found!")
            return 1
        
        test_claim = pending_claims[0]
        print(f"Test claim: {test_claim['claim_number']} (Row {test_claim['excel_row']})")
        
        # Mark it as completed
        print(f"\n2. Marking claim {test_claim['claim_number']} as completed...")
        success = processor.mark_claim_completed(
            test_claim['claim_number'], 
            test_claim['row_index']
        )
        
        if success:
            print("✓ Successfully marked claim as completed")
        else:
            print("❌ Failed to mark claim as completed")
            return 1
        
        # Verify the update
        print("\n3. Verifying the update...")
        updated_claims = processor.load_claims()
        
        # Find our test claim
        updated_claim = None
        for claim in updated_claims:
            if claim['claim_number'] == test_claim['claim_number']:
                updated_claim = claim
                break
        
        if updated_claim and updated_claim['is_completed']:
            print("✓ Claim is now marked as completed in Excel")
        else:
            print("❌ Claim was not properly marked as completed")
            return 1
        
        # Show updated summary
        print("\n4. Updated summary:")
        completed_count = sum(1 for claim in updated_claims if claim['is_completed'])
        pending_count = len(updated_claims) - completed_count
        print(f"   Total claims: {len(updated_claims)}")
        print(f"   Completed: {completed_count}")
        print(f"   Pending: {pending_count}")
        
        print("\n✅ Excel update test completed successfully!")
        print(f"Note: Claim {test_claim['claim_number']} is now marked as completed.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
