@echo off
echo Starting Chrome with Debug Port for XactAnalysis Automation
echo ============================================================

REM Close any existing Chrome instances first
echo Closing existing Chrome instances...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

REM Create a temporary user data directory
set TEMP_PROFILE=%TEMP%\chrome_debug_profile
if exist "%TEMP_PROFILE%" rmdir /s /q "%TEMP_PROFILE%"
mkdir "%TEMP_PROFILE%"

echo.
echo Starting Chrome with debug port 9222...
echo.
echo Chrome will open with:
echo - Remote debugging enabled on port 9222
echo - Temporary profile (clean session)
echo - Web security disabled for automation
echo.

REM Try different Chrome installation paths
set CHROME_PATH=""

REM Check common Chrome installation paths
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
) else (
    echo ERROR: Chrome not found in common installation paths!
    echo.
    echo Please install Chrome or update the path in this script.
    echo Common paths to check:
    echo - C:\Program Files\Google\Chrome\Application\chrome.exe
    echo - C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
    echo - %LOCALAPPDATA%\Google\Chrome\Application\chrome.exe
    echo.
    pause
    exit /b 1
)

echo Found Chrome at: %CHROME_PATH%
echo.

REM Launch Chrome with debug flags
start "Chrome Debug" %CHROME_PATH% ^
    --remote-debugging-port=9222 ^
    --user-data-dir="%TEMP_PROFILE%" ^
    --disable-web-security ^
    --disable-features=VizDisplayCompositor ^
    --disable-blink-features=AutomationControlled ^
    --no-first-run ^
    --no-default-browser-check ^
    --disable-default-apps ^
    --disable-popup-blocking ^
    "https://www.xactanalysis.com"

echo.
echo Chrome should now be starting with debug port 9222...
echo.
echo Next steps:
echo 1. Wait for Chrome to fully load
echo 2. Log into XactAnalysis in the Chrome window
echo 3. Go back to the web automation interface
echo 4. Click "Connect to Browser"
echo.
echo If you see connection issues, try:
echo - Waiting a few more seconds for Chrome to fully start
echo - Checking if port 9222 is already in use
echo - Running this script as Administrator
echo.

REM Test if the debug port is responding
echo Testing debug port connection...
timeout /t 3 >nul

REM Use curl to test if debug port is responding (if available)
curl -s http://localhost:9222/json/version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Debug port 9222 is responding!
) else (
    echo ⚠️  Debug port not responding yet - give it a few more seconds
)

echo.
echo Press any key to close this window...
pause >nul
