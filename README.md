# XactAnalysis Claim Note Extraction Automation

An automated system for extracting claim notes from XactAnalysis, converting them to PDFs, and tracking completion status in Excel.

## Overview

This automation tool:
- Reads claim numbers from an Excel file
- Logs into XactAnalysis via browser automation
- Extracts notes for each claim and saves them as PDFs
- Tracks completion status in the Excel file
- Provides comprehensive logging and error handling

## Features

- **Robust Browser Automation**: Uses Playwright for reliable web automation
- **Excel Integration**: Seamless reading and updating of Excel files
- **PDF Generation**: Direct print-to-PDF functionality
- **Error Handling**: Comprehensive error categorization and retry mechanisms
- **Secure Credentials**: Safe handling of login credentials
- **Logging**: Detailed logging with timestamps and severity levels
- **Configuration**: Flexible configuration management

## Project Structure

```
XA Extraction/
├── src/
│   ├── automation/
│   │   ├── __init__.py
│   │   ├── browser_manager.py
│   │   ├── xact_analysis_bot.py
│   │   └── pdf_generator.py
│   ├── data/
│   │   ├── __init__.py
│   │   └── excel_processor.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── config_manager.py
│   │   ├── logger.py
│   │   └── exceptions.py
│   └── main.py
├── config/
│   ├── config.ini
│   └── config.example.ini
├── tests/
├── logs/
├── requirements.txt
├── setup.py
└── README.md
```

## Installation

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment: `venv\Scripts\activate` (Windows)
4. Install dependencies: `pip install -r requirements.txt`
5. Copy `config/config.example.ini` to `config/config.ini` and configure

## Usage

```bash
python src/main.py
```

## Configuration

Edit `config/config.ini` to set:
- Excel file path
- Output directory for PDFs
- Browser settings
- Logging preferences

## Security

- Never commit credentials to version control
- Use environment variables or secure credential storage
- Keep the `config/credentials.ini` file private

## Contributing

1. Follow PEP 8 style guidelines
2. Add tests for new functionality
3. Update documentation as needed
4. Use meaningful commit messages

## License

Private project - All rights reserved
