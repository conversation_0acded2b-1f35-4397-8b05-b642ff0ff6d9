"""
PDF extraction module for XactAnalysis notes using Beautiful Soup.

This module handles extracting and downloading PDF notes from XactAnalysis
claims using HTTP requests and HTML parsing.
"""

import os
import time
import re
from pathlib import Path
from typing import Optional, Dict, Any, List
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from .xact_session import XactSession
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import PDFGenerationError, NavigationError


class PDFExtractor:
    """Handles PDF extraction from XactAnalysis notes."""
    
    def __init__(self, session: XactSession, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the PDF extractor.
        
        Args:
            session: Authenticated XactAnalysis session
            config_manager: Configuration manager instance
        """
        self.session = session
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.output_dir = Path(self.config.get('paths', 'output_dir', 'output'))
        self.filename_template = self.config.get('pdf', 'filename_template', '{claim_number}{suffix}.pdf')
        self.multiple_suffixes = self.config.get('pdf', 'multiple_suffixes', '_A,_B,_C,_D,_E,_F,_G,_H,_I,_J').split(',')
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("PDF extractor initialized")
    
    def extract_notes_pdf(self, claim_number: str, notes_url: str, suffix_index: int = 0) -> str:
        """
        Extract notes as PDF for a specific claim.
        
        Args:
            claim_number: The claim number
            notes_url: URL of the notes page
            suffix_index: Index for multiple claims (0 for first, 1 for second, etc.)
            
        Returns:
            Path to the saved PDF file
            
        Raises:
            PDFGenerationError: If PDF extraction fails
        """
        try:
            self.logger.info(f"Extracting PDF for claim {claim_number} from {notes_url}")
            
            # Get the notes page
            notes_response = self.session.get(notes_url)
            soup = BeautifulSoup(notes_response.content, 'html.parser')
            
            # Try different methods to get PDF
            pdf_path = None
            
            # Method 1: Look for direct PDF download links
            pdf_path = self._try_direct_pdf_download(soup, claim_number, suffix_index)
            
            if not pdf_path:
                # Method 2: Look for print/export buttons that generate PDFs
                pdf_path = self._try_print_to_pdf(soup, claim_number, suffix_index)
            
            if not pdf_path:
                # Method 3: Generate PDF from HTML content
                pdf_path = self._generate_pdf_from_html(soup, claim_number, suffix_index)
            
            if not pdf_path:
                raise PDFGenerationError(f"Could not extract PDF for claim {claim_number}")
            
            self.logger.info(f"Successfully extracted PDF: {pdf_path}")
            return str(pdf_path)
            
        except Exception as e:
            if isinstance(e, PDFGenerationError):
                raise
            self.logger.error(f"PDF extraction failed for claim {claim_number}: {e}")
            raise PDFGenerationError(f"PDF extraction failed: {e}")
    
    def _try_direct_pdf_download(self, soup: BeautifulSoup, claim_number: str, suffix_index: int) -> Optional[str]:
        """
        Try to find and download a direct PDF link.
        
        Args:
            soup: BeautifulSoup object of the notes page
            claim_number: Claim number
            suffix_index: Suffix index for multiple claims
            
        Returns:
            Path to downloaded PDF or None
        """
        try:
            # Look for PDF download links
            pdf_links = []
            
            # Find links that point to PDFs
            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text().strip().lower()
                
                # Check if this is a PDF link
                if (href.lower().endswith('.pdf') or 
                    'pdf' in href.lower() or
                    'download' in href.lower() or
                    'export' in href.lower() or
                    'print' in link_text or
                    'pdf' in link_text or
                    'download' in link_text):
                    
                    pdf_links.append(urljoin(self.session.base_url, href))
            
            if not pdf_links:
                return None
            
            # Try to download the first PDF link
            pdf_url = pdf_links[0]
            self.logger.debug(f"Attempting to download PDF from: {pdf_url}")
            
            pdf_response = self.session.get(pdf_url)
            
            # Check if response is actually a PDF
            content_type = pdf_response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not pdf_url.lower().endswith('.pdf'):
                self.logger.debug("Response doesn't appear to be a PDF")
                return None
            
            # Save the PDF
            filename = self._generate_filename(claim_number, suffix_index)
            pdf_path = self.output_dir / filename
            
            with open(pdf_path, 'wb') as f:
                f.write(pdf_response.content)
            
            self.logger.debug(f"Downloaded PDF to: {pdf_path}")
            return str(pdf_path)
            
        except Exception as e:
            self.logger.warning(f"Direct PDF download failed: {e}")
            return None
    
    def _try_print_to_pdf(self, soup: BeautifulSoup, claim_number: str, suffix_index: int) -> Optional[str]:
        """
        Try to find and use print/export functionality to generate PDF.
        
        Args:
            soup: BeautifulSoup object of the notes page
            claim_number: Claim number
            suffix_index: Suffix index for multiple claims
            
        Returns:
            Path to generated PDF or None
        """
        try:
            # Look for print/export buttons or forms
            print_elements = []
            
            # Find buttons or links that might trigger PDF generation
            for element in soup.find_all(['button', 'a', 'input'], href=True):
                element_text = element.get_text().strip().lower()
                href = element.get('href', '').lower()
                onclick = element.get('onclick', '').lower()
                
                if any(keyword in element_text + href + onclick 
                      for keyword in ['print', 'pdf', 'export', 'download']):
                    print_elements.append(element)
            
            if not print_elements:
                return None
            
            # Try to trigger PDF generation
            for element in print_elements:
                try:
                    if element.name == 'a' and element.get('href'):
                        # It's a link - follow it
                        print_url = urljoin(self.session.base_url, element['href'])
                        print_response = self.session.get(print_url)
                        
                        # Check if this gives us a PDF
                        content_type = print_response.headers.get('content-type', '').lower()
                        if 'pdf' in content_type:
                            filename = self._generate_filename(claim_number, suffix_index)
                            pdf_path = self.output_dir / filename
                            
                            with open(pdf_path, 'wb') as f:
                                f.write(print_response.content)
                            
                            return str(pdf_path)
                    
                    elif element.name in ['button', 'input']:
                        # It's a form element - try to submit the form
                        form = element.find_parent('form')
                        if form:
                            form_action = form.get('action', '')
                            form_method = form.get('method', 'post').lower()
                            
                            # Build form data
                            form_data = {}
                            for input_field in form.find_all('input'):
                                field_name = input_field.get('name')
                                field_value = input_field.get('value', '')
                                if field_name:
                                    form_data[field_name] = field_value
                            
                            # Submit the form
                            form_url = urljoin(self.session.base_url, form_action)
                            if form_method == 'get':
                                form_response = self.session.get(form_url, params=form_data)
                            else:
                                form_response = self.session.post(form_url, data=form_data)
                            
                            # Check if this gives us a PDF
                            content_type = form_response.headers.get('content-type', '').lower()
                            if 'pdf' in content_type:
                                filename = self._generate_filename(claim_number, suffix_index)
                                pdf_path = self.output_dir / filename
                                
                                with open(pdf_path, 'wb') as f:
                                    f.write(form_response.content)
                                
                                return str(pdf_path)
                
                except Exception as e:
                    self.logger.debug(f"Print element failed: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Print to PDF failed: {e}")
            return None
    
    def _generate_pdf_from_html(self, soup: BeautifulSoup, claim_number: str, suffix_index: int) -> Optional[str]:
        """
        Generate PDF from HTML content using reportlab.
        
        Args:
            soup: BeautifulSoup object of the notes page
            claim_number: Claim number
            suffix_index: Suffix index for multiple claims
            
        Returns:
            Path to generated PDF or None
        """
        try:
            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            
            # Extract notes content
            notes_content = self._extract_notes_content(soup)
            
            if not notes_content:
                return None
            
            # Generate filename
            filename = self._generate_filename(claim_number, suffix_index)
            pdf_path = self.output_dir / filename
            
            # Create PDF
            doc = SimpleDocTemplate(str(pdf_path), pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
            )
            story.append(Paragraph(f"Claim Notes - {claim_number}", title_style))
            story.append(Spacer(1, 12))
            
            # Content
            for content_item in notes_content:
                if content_item.strip():
                    # Clean up the text
                    clean_text = re.sub(r'\s+', ' ', content_item.strip())
                    story.append(Paragraph(clean_text, styles['Normal']))
                    story.append(Spacer(1, 12))
            
            # Build PDF
            doc.build(story)
            
            self.logger.debug(f"Generated PDF from HTML: {pdf_path}")
            return str(pdf_path)
            
        except ImportError:
            self.logger.warning("reportlab not available for HTML to PDF conversion")
            return None
        except Exception as e:
            self.logger.warning(f"HTML to PDF generation failed: {e}")
            return None
    
    def _extract_notes_content(self, soup: BeautifulSoup) -> List[str]:
        """
        Extract notes content from the HTML.
        
        Args:
            soup: BeautifulSoup object of the notes page
            
        Returns:
            List of text content from notes
        """
        content = []
        
        # Look for notes in various containers
        notes_containers = [
            soup.find('div', class_=lambda cls: cls and 'note' in str(cls).lower()),
            soup.find('section', class_=lambda cls: cls and 'note' in str(cls).lower()),
            soup.find('div', {'id': lambda id_val: id_val and 'note' in id_val.lower()}),
            soup.find('textarea'),
            soup.find('div', class_=lambda cls: cls and 'content' in str(cls).lower()),
        ]
        
        # Also look for any element containing "note" text
        for element in soup.find_all(text=lambda text: text and 'note' in text.lower()):
            parent = element.parent
            if parent and parent.name not in ['script', 'style']:
                notes_containers.append(parent)
        
        # Extract text from containers
        for container in notes_containers:
            if container:
                text = container.get_text().strip()
                if text and len(text) > 10:  # Only include substantial content
                    content.append(text)
        
        # If no specific notes containers found, get main content
        if not content:
            main_content = soup.find('main') or soup.find('body')
            if main_content:
                # Remove navigation, headers, footers
                for unwanted in main_content.find_all(['nav', 'header', 'footer', 'script', 'style']):
                    unwanted.decompose()
                
                text = main_content.get_text().strip()
                if text:
                    content.append(text)
        
        return content
    
    def _generate_filename(self, claim_number: str, suffix_index: int) -> str:
        """
        Generate filename for the PDF.
        
        Args:
            claim_number: Claim number
            suffix_index: Index for multiple claims
            
        Returns:
            Generated filename
        """
        if suffix_index == 0:
            suffix = ''
        else:
            suffix = self.multiple_suffixes[min(suffix_index - 1, len(self.multiple_suffixes) - 1)]
        
        filename = self.filename_template.format(
            claim_number=claim_number,
            suffix=suffix
        )
        
        return filename
