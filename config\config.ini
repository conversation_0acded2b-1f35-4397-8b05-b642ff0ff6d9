[DEFAULT]
# XactAnalysis Automation Configuration

[paths]
# Excel file containing claim numbers (relative to project root)
excel_file = User Input/Adam Jobs in 24.xlsx - Sheet2.csv
# Output directory for PDF files
output_dir = output
# Log directory
log_dir = logs
# Screenshots directory (for debugging)
screenshot_dir = screenshots

[excel]
# Column containing claim numbers (0-based index, Claim #: = 1)
claim_column = 1
# Column for completion status (0-based index, A = 0)
status_column = 0
# Status marker for completed claims
completion_marker = D
# Sheet name (leave empty for first sheet)
sheet_name = 

[browser]
# Browser type: chromium, firefox, webkit
browser_type = chromium
# Run in headless mode (true/false)
headless = false
# Browser timeout in milliseconds
timeout = 30000
# Page load timeout in milliseconds
page_timeout = 60000
# Download timeout in milliseconds
download_timeout = 120000

[xactanalysis]
# XactAnalysis website URL
base_url = https://www.xactanalysis.com
# Login page URL (if different from base)
login_url = 
# Search delay in seconds (to avoid being too fast)
search_delay = 2
# Page navigation delay in seconds
nav_delay = 1

[pdf]
# PDF file naming convention
# Available variables: {claim_number}, {suffix}
filename_template = {claim_number}{suffix}.pdf
# Suffix for multiple entries
multiple_suffixes = _A,_B,_C,_D,_E,_F,_G,_H,_I,_J

[logging]
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
log_level = INFO
# Log format
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
# Maximum log file size in MB
max_log_size = 10
# Number of backup log files to keep
backup_count = 5

[retry]
# Maximum number of retries for failed operations
max_retries = 3
# Base delay between retries in seconds
retry_delay = 5
# Exponential backoff multiplier
backoff_multiplier = 2

[security]
# Use environment variables for credentials (recommended)
use_env_credentials = true
# Credential storage method: env, keyring, prompt
credential_method = env
