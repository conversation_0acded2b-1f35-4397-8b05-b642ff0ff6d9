#!/usr/bin/env python3
"""
Launcher script for the XactAnalysis automation web application.
"""

import sys
import webbrowser
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.web_app import app, logger

def main():
    """Launch the web application."""
    try:
        print("🚀 Starting XactAnalysis Automation Web Application...")
        print("=" * 60)
        print()
        print("📋 Instructions:")
        print("1. Start Chrome with debug port:")
        print("   chrome.exe --remote-debugging-port=9222")
        print()
        print("2. Log into XactAnalysis in the browser")
        print()
        print("3. Open the web interface (will open automatically)")
        print()
        print("4. Click 'Connect to Browser' in the web interface")
        print()
        print("5. Use the buttons to process claims!")
        print()
        print("=" * 60)
        
        # Start the Flask app in a separate thread
        import threading
        
        def run_app():
            app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
        
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
        
        # Wait a moment for the server to start
        time.sleep(2)
        
        # Open browser
        url = "http://127.0.0.1:5000"
        print(f"🌐 Opening web interface: {url}")
        webbrowser.open(url)
        
        print()
        print("✅ Web application is running!")
        print("   Press Ctrl+C to stop")
        print()
        
        # Keep the main thread alive
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
            
    except Exception as e:
        print(f"❌ Error starting web application: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
