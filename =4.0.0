Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: flask in c:\users\<USER>\appdata\roaming\python\python313\site-packages (3.1.1)
Collecting flask-cors
  Downloading flask_cors-6.0.1-py3-none-any.whl.metadata (5.3 kB)
Requirement already satisfied: blinker>=1.9.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from flask) (1.9.0)
Requirement already satisfied: click>=8.1.3 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from flask) (8.2.1)
Requirement already satisfied: itsdangerous>=2.2.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from flask) (2.2.0)
Requirement already satisfied: jinja2>=3.1.2 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from flask) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from flask) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from flask) (3.1.3)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from click>=8.1.3->flask) (0.4.6)
Downloading flask_cors-6.0.1-py3-none-any.whl (13 kB)
Installing collected packages: flask-cors
Successfully installed flask-cors-6.0.1
