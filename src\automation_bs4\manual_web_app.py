"""
Manual login web interface for XactAnalysis automation.

This module provides a web interface that uses manual login with
Beautiful Soup processing.
"""

import os
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from werkzeug.utils import secure_filename

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash

from .manual_controller import ManualAutomationController
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger


# Initialize Flask app
app = Flask(__name__, 
           template_folder='../../templates_bs4',
           static_folder='../../static')
app.secret_key = os.urandom(24)

# Initialize components
config = ConfigManager()
logger = get_logger(__name__, config)
automation_controller = ManualAutomationController(config)

# Configuration
UPLOAD_FOLDER = Path('uploads')
UPLOAD_FOLDER.mkdir(exist_ok=True)
ALLOWED_EXTENSIONS = {'csv'}


def allowed_file(filename):
    """Check if file has allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/')
def index():
    """Main dashboard page."""
    try:
        status = automation_controller.get_status()
        return render_template('manual_index.html', 
                             status=status,
                             current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        return render_template('error.html', error=str(e))


@app.route('/api/status')
def api_status():
    """Get current system status."""
    try:
        status = automation_controller.get_status()
        return jsonify({'success': True, 'status': status})
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/upload_csv', methods=['POST'])
def api_upload_csv():
    """Upload and load a CSV file."""
    try:
        if 'csv_file' not in request.files:
            return jsonify({'success': False, 'message': 'No file uploaded'}), 400
        
        file = request.files['csv_file']
        
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'message': 'Invalid file type. Please upload a CSV file.'}), 400
        
        # Save the uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = UPLOAD_FOLDER / filename
        
        file.save(file_path)
        logger.info(f"CSV file uploaded: {file_path}")
        
        # Load the CSV file
        result = automation_controller.load_csv_file(str(file_path))
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"CSV upload error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/start_manual_login', methods=['POST'])
def api_start_manual_login():
    """Start the manual login process."""
    try:
        result = automation_controller.start_manual_login()
        return jsonify(result)
    except Exception as e:
        logger.error(f"Start manual login error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/extract_session', methods=['POST'])
def api_extract_session():
    """Get instructions for session extraction."""
    try:
        result = automation_controller.extract_session()
        return jsonify(result)
    except Exception as e:
        logger.error(f"Extract session error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/load_cookies', methods=['POST'])
def api_load_cookies():
    """Load cookies from user text."""
    try:
        data = request.get_json()
        cookies_text = data.get('cookies_text', '').strip()
        
        if not cookies_text:
            return jsonify({'success': False, 'message': 'No cookies provided'}), 400
        
        result = automation_controller.load_cookies_from_text(cookies_text)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Load cookies error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/load_saved_session', methods=['POST'])
def api_load_saved_session():
    """Load previously saved session."""
    try:
        result = automation_controller.load_saved_session()
        return jsonify(result)
    except Exception as e:
        logger.error(f"Load saved session error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/process_claim', methods=['POST'])
def api_process_claim():
    """Process a specific claim."""
    try:
        data = request.get_json()
        claim_number = data.get('claim_number', '').strip()
        
        if not claim_number:
            return jsonify({'success': False, 'message': 'Claim number is required'}), 400
        
        result = automation_controller.process_single_claim(claim_number)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Process claim error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/process_next', methods=['POST'])
def api_process_next():
    """Process the next pending claim."""
    try:
        result = automation_controller.process_next_claim()
        return jsonify(result)
    except Exception as e:
        logger.error(f"Process next error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/process_all', methods=['POST'])
def api_process_all():
    """Process all pending claims."""
    try:
        data = request.get_json() or {}
        max_claims = data.get('max_claims')
        
        result = automation_controller.process_all_claims(max_claims)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Process all error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/csv_summary')
def api_csv_summary():
    """Get CSV file summary."""
    try:
        if not automation_controller.current_csv_file:
            return jsonify({'success': False, 'message': 'No CSV file loaded'})
        
        summary = automation_controller.csv_processor.get_file_summary()
        return jsonify({'success': True, 'summary': summary})
        
    except Exception as e:
        logger.error(f"CSV summary error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors."""
    return render_template('error.html', error="Page not found"), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {error}")
    return render_template('error.html', error="Internal server error"), 500


@app.teardown_appcontext
def cleanup(error):
    """Cleanup resources on app context teardown."""
    if error:
        logger.error(f"App context error: {error}")


def create_app():
    """Create and configure the Flask app."""
    return app


if __name__ == '__main__':
    try:
        logger.info("Starting Manual Login XactAnalysis Automation Web App")
        app.run(debug=False, host='127.0.0.1', port=5002)
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        automation_controller.cleanup()
