#!/usr/bin/env python3
"""
Launch script for the Beautiful Soup-based XactAnalysis automation.

This script starts the automated XactAnalysis processor with a simple web interface.
"""

import os
import sys
import webbrowser
import threading
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.automation_bs4.web_app import app
from src.utils.config_manager import ConfigManager


def print_banner():
    """Print application banner."""
    print("=" * 70)
    print("🤖 XactAnalysis Automated Processor (Beautiful Soup Edition)")
    print("=" * 70)
    print()
    print("✨ Features:")
    print("  • Fully automated login and processing")
    print("  • No browser setup required")
    print("  • Simple CSV upload interface")
    print("  • Automatic PDF generation and naming")
    print("  • Real-time progress tracking")
    print()


def check_environment():
    """Check if environment is properly configured."""
    issues = []
    
    # Check for credentials
    if not os.getenv('XACT_USERNAME'):
        issues.append("XACT_USERNAME environment variable not set")
    
    if not os.getenv('XACT_PASSWORD'):
        issues.append("XACT_PASSWORD environment variable not set")
    
    # Check for required directories
    required_dirs = ['output', 'logs', 'uploads']
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created directory: {dir_name}")
    
    return issues


def print_instructions():
    """Print usage instructions."""
    print("📋 Instructions:")
    print("1. Make sure your XactAnalysis credentials are set:")
    print("   - XACT_USERNAME=your_username")
    print("   - XACT_PASSWORD=your_password")
    print()
    print("2. Open the web interface (will open automatically)")
    print()
    print("3. Upload your CSV file with claim numbers")
    print()
    print("4. Click 'Login to XactAnalysis'")
    print()
    print("5. Process claims individually or all at once!")
    print()
    print("🎯 That's it! The system handles everything automatically.")
    print()


def open_browser_delayed(url, delay=2):
    """Open browser after a delay."""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 Opened browser: {url}")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print(f"   Please open manually: {url}")


def main():
    """Main entry point."""
    print_banner()
    
    # Check environment
    issues = check_environment()
    
    if issues:
        print("❌ Environment Issues Found:")
        for issue in issues:
            print(f"   • {issue}")
        print()
        print("💡 To fix:")
        print("   1. Copy .env_bs4.example to .env")
        print("   2. Edit .env and add your XactAnalysis credentials")
        print("   3. Run this script again")
        print()
        return 1
    
    print("✅ Environment check passed!")
    print()
    
    print_instructions()
    
    try:
        # Load configuration
        config = ConfigManager()
        
        # Get web app settings
        host = config.get('web', 'host', '127.0.0.1')
        port = config.get_int('web', 'port', 5001)
        debug = config.get_bool('web', 'debug', False)
        
        url = f"http://{host}:{port}"
        
        print(f"🚀 Starting XactAnalysis Automated Processor...")
        print(f"   Host: {host}")
        print(f"   Port: {port}")
        print(f"   URL: {url}")
        print()
        print("Press Ctrl+C to stop the application")
        print("=" * 70)
        
        # Open browser in background
        browser_thread = threading.Thread(
            target=open_browser_delayed, 
            args=(url, 3),
            daemon=True
        )
        browser_thread.start()
        
        # Start the Flask app
        app.run(
            debug=debug,
            host=host,
            port=port,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
