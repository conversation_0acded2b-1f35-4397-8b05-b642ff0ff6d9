# Browser Extension Installation Guide

## 🚀 Quick Setup (No Chrome Debug Port Needed!)

This browser extension provides a much simpler alternative to Chrome debug ports. It works directly in your browser without any special Chrome startup commands.

### Step 1: Install the Extension

1. **Open Chrome** (normal startup, no special commands needed!)

2. **Go to Extensions page:**
   - Type `chrome://extensions/` in the address bar
   - OR click the three dots menu → More tools → Extensions

3. **Enable Developer Mode:**
   - Toggle the "Developer mode" switch in the top-right corner

4. **Load the Extension:**
   - Click "Load unpacked"
   - Navigate to your project folder: `XA Extraction`
   - Select the `browser_extension` folder
   - Click "Select Folder"

5. **Pin the Extension:**
   - Click the puzzle piece icon (🧩) in Chrome toolbar
   - Find "XactAnalysis Automation Helper"
   - Click the pin icon to keep it visible

### Step 2: Start the Web App

1. **Run the web application:**
   ```bash
   python run_web_app.py
   ```

2. **The web app will open at:** `http://127.0.0.1:5000`

### Step 3: Use the Extension

1. **Navigate to XactAnalysis** in Chrome:
   - Go to `https://www.xactanalysis.com`
   - Log into your account

2. **Click the Extension Icon** (🤖) in the Chrome toolbar

3. **Connect to Web App:**
   - Click "Connect to Web App" in the extension popup
   - You should see "✅ Connected to Web App"

4. **Start Automating:**
   - Enter a claim number and click "🔍 Search"
   - Click "📝 Go to Notes" to navigate to notes
   - Click "📄 Generate PDF" to create PDF
   - OR click "⏭️ Process Next Claim" for automatic processing

## 🎯 How It Works

### Extension Features:
- **🔗 Direct Communication:** Extension talks directly to the web app
- **🔍 Smart Search:** Automatically finds and fills search fields
- **📝 Navigation:** Clicks Notes tabs/links automatically  
- **📄 PDF Generation:** Triggers browser's print-to-PDF function
- **⏭️ Queue Processing:** Gets next claim from your CSV file
- **✅ Auto-Completion:** Marks claims as done in your Excel/CSV

### No More Chrome Debug Issues:
- ❌ No special Chrome startup commands
- ❌ No debug port conflicts
- ❌ No administrator privileges needed
- ❌ No firewall configuration
- ✅ Works with normal Chrome
- ✅ Simple browser extension
- ✅ Direct web app integration

## 🔧 Troubleshooting

### Extension Not Loading:
1. Make sure Developer Mode is enabled
2. Check that you selected the `browser_extension` folder (not the main project folder)
3. Look for error messages in the Extensions page

### Connection Issues:
1. Make sure the web app is running (`python run_web_app.py`)
2. Check that you can access `http://127.0.0.1:5000` in your browser
3. Try refreshing the XactAnalysis page

### Search Not Working:
1. Make sure you're on a XactAnalysis page with a search field
2. Try entering the claim number manually first to see the expected behavior
3. Check the browser console (F12) for error messages

### PDF Generation Issues:
1. The extension triggers Chrome's print dialog
2. In the print dialog, choose "Save as PDF" as the destination
3. Save the PDF with the claim number as filename

## 🎉 Advantages Over Debug Port Method

| Feature | Debug Port | Browser Extension |
|---------|------------|-------------------|
| Setup Complexity | High | Low |
| Chrome Startup | Special commands | Normal startup |
| Permissions | Administrator | None |
| Firewall Issues | Common | None |
| Port Conflicts | Frequent | Never |
| User Experience | Technical | Simple |
| Reliability | Variable | High |

## 📋 Usage Workflow

### Manual Processing:
1. Open XactAnalysis and log in
2. Click extension icon
3. Connect to web app
4. Enter claim number → Search → Notes → PDF
5. Repeat for each claim

### Automatic Processing:
1. Open XactAnalysis and log in
2. Click extension icon
3. Connect to web app
4. Click "Process Next Claim"
5. Extension automatically:
   - Gets next claim from CSV
   - Searches for it
   - You manually navigate and generate PDF
   - Marks it complete in CSV
   - Moves to next claim

## 🔄 Updates and Maintenance

### Updating the Extension:
1. Make changes to extension files
2. Go to `chrome://extensions/`
3. Click the refresh icon (🔄) on the extension card

### Removing the Extension:
1. Go to `chrome://extensions/`
2. Click "Remove" on the extension card

## 🆘 Support

If you encounter issues:

1. **Check Browser Console:** Press F12 → Console tab for error messages
2. **Check Extension Console:** Go to `chrome://extensions/` → Click "Inspect views: service worker"
3. **Check Web App Logs:** Look at the terminal running the web app
4. **Restart Everything:** Close Chrome, restart web app, reload extension

This extension approach is much more reliable and user-friendly than Chrome debug ports!
