"""
Browser management for XactAnalysis automation.

This module handles browser connections, session management, and provides
a foundation for interacting with XactAnalysis through an existing session.
"""

import time
import threading
import queue
from typing import Optional, List, Dict, Any, Callable
from pathlib import Path
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, Page

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import BrowserError, NavigationError, TimeoutError


class BrowserManager:
    """Manages browser connections and sessions for XactAnalysis automation."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the browser manager.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)

        # Configuration
        self.browser_type = self.config.get('browser', 'browser_type', 'chromium')
        self.headless = self.config.get_bool('browser', 'headless', False)
        self.timeout = self.config.get_int('browser', 'timeout', 30000)
        self.page_timeout = self.config.get_int('browser', 'page_timeout', 60000)

        # Browser instances
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

        # Thread safety
        self._lock = threading.RLock()
        self._connection_thread_id = None
        self._operation_queue = queue.Queue()
        self._result_queue = queue.Queue()

        self.logger.info("Browser manager initialized")
    
    def connect_to_existing_session(self, debug_port: int = 9222) -> bool:
        """
        Connect to an existing browser session.

        Args:
            debug_port: Chrome debug port (default 9222)

        Returns:
            True if connection successful, False otherwise
        """
        with self._lock:
            try:
                self.logger.info(f"Attempting to connect to existing browser session on port {debug_port}")

                # Store the thread ID that creates the connection
                self._connection_thread_id = threading.get_ident()

                self.playwright = sync_playwright().start()

                # Connect to existing browser
                self.browser = self.playwright.chromium.connect_over_cdp(f"http://localhost:{debug_port}")

                # Get existing contexts
                contexts = self.browser.contexts
                if not contexts:
                    self.logger.warning("No existing browser contexts found")
                    return False

                # Use the first context
                self.context = contexts[0]

                # Get existing pages
                pages = self.context.pages
                if not pages:
                    self.logger.warning("No existing pages found")
                    return False

                # Find XactAnalysis page or use the first page
                xa_page = None
                for page in pages:
                    try:
                        url = page.url
                        if 'xactanalysis' in url.lower():
                            xa_page = page
                            break
                    except:
                        continue

                self.page = xa_page or pages[0]

                # Set timeouts
                self.page.set_default_timeout(self.timeout)
                self.page.set_default_navigation_timeout(self.page_timeout)

                self.logger.info(f"Successfully connected to existing session. Current URL: {self.page.url}")
                return True

            except Exception as e:
                self.logger.error(f"Failed to connect to existing session: {e}")
                self.cleanup()
                return False
    
    def launch_new_session(self) -> bool:
        """
        Launch a new browser session.
        
        Returns:
            True if launch successful, False otherwise
        """
        try:
            self.logger.info("Launching new browser session")
            
            self.playwright = sync_playwright().start()
            
            # Browser launch options
            launch_options = {
                'headless': self.headless,
                'args': [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            }
            
            # Launch browser
            if self.browser_type == 'chromium':
                self.browser = self.playwright.chromium.launch(**launch_options)
            elif self.browser_type == 'firefox':
                self.browser = self.playwright.firefox.launch(**launch_options)
            elif self.browser_type == 'webkit':
                self.browser = self.playwright.webkit.launch(**launch_options)
            else:
                raise BrowserError(f"Unsupported browser type: {self.browser_type}")
            
            # Create context
            self.context = self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            
            # Create page
            self.page = self.context.new_page()
            
            # Set timeouts
            self.page.set_default_timeout(self.timeout)
            self.page.set_default_navigation_timeout(self.page_timeout)
            
            self.logger.info("Successfully launched new browser session")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to launch new session: {e}")
            self.cleanup()
            return False
    
    def navigate_to_xactanalysis(self) -> bool:
        """
        Navigate to XactAnalysis website.
        
        Returns:
            True if navigation successful, False otherwise
        """
        try:
            if not self.page:
                raise BrowserError("No active page available")
            
            base_url = self.config.get('xactanalysis', 'base_url', 'https://www.xactanalysis.com')
            
            self.logger.info(f"Navigating to XactAnalysis: {base_url}")
            self.page.goto(base_url)
            
            # Wait for page to load
            self.page.wait_for_load_state('networkidle')
            
            self.logger.info("Successfully navigated to XactAnalysis")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to XactAnalysis: {e}")
            return False
    
    def _get_current_url_impl(self) -> str:
        """Internal implementation of get current URL."""
        return self.page.url

    def _get_page_title_impl(self) -> str:
        """Internal implementation of get page title."""
        return self.page.title()

    def get_current_url(self) -> Optional[str]:
        """Get the current page URL."""
        return self._safe_execute(self._get_current_url_impl)

    def get_page_title(self) -> Optional[str]:
        """Get the current page title."""
        return self._safe_execute(self._get_page_title_impl)

    def _query_selector_impl(self, selector: str):
        """Internal implementation of query selector."""
        return self.page.query_selector(selector)

    def _query_selector_all_impl(self, selector: str):
        """Internal implementation of query selector all."""
        return self.page.query_selector_all(selector)

    def _fill_impl(self, selector: str, value: str):
        """Internal implementation of fill."""
        return self.page.fill(selector, value)

    def _press_impl(self, selector: str, key: str):
        """Internal implementation of press."""
        return self.page.press(selector, key)

    def _click_impl(self, selector: str):
        """Internal implementation of click."""
        return self.page.click(selector)

    def safe_query_selector(self, selector: str):
        """Safely query for a selector."""
        return self._safe_execute(self._query_selector_impl, selector)

    def safe_query_selector_all(self, selector: str):
        """Safely query for all matching selectors."""
        return self._safe_execute(self._query_selector_all_impl, selector)

    def safe_fill(self, selector: str, value: str) -> bool:
        """Safely fill an input field."""
        result = self._safe_execute(self._fill_impl, selector, value)
        return result is not None

    def safe_press(self, selector: str, key: str) -> bool:
        """Safely press a key on an element."""
        result = self._safe_execute(self._press_impl, selector, key)
        return result is not None

    def safe_click(self, selector: str) -> bool:
        """Safely click an element."""
        result = self._safe_execute(self._click_impl, selector)
        return result is not None
    
    def _take_screenshot_impl(self, screenshot_path: Path) -> Path:
        """Internal implementation of screenshot taking."""
        self.page.screenshot(path=str(screenshot_path))
        return screenshot_path

    def take_screenshot(self, filename: Optional[str] = None) -> Optional[Path]:
        """
        Take a screenshot of the current page.

        Args:
            filename: Optional filename for the screenshot

        Returns:
            Path to the screenshot file if successful, None otherwise
        """
        try:
            screenshot_dir = self.config.get_path('paths', 'screenshot_dir', Path('screenshots'))
            screenshot_dir.mkdir(parents=True, exist_ok=True)

            if not filename:
                timestamp = int(time.time())
                filename = f"screenshot_{timestamp}.png"

            screenshot_path = screenshot_dir / filename

            # Use safe execution to handle threading issues
            result = self._safe_execute(self._take_screenshot_impl, screenshot_path)

            if result:
                self.logger.debug(f"Screenshot saved: {screenshot_path}")
                return result
            else:
                self.logger.warning("Screenshot failed due to browser connection issues")
                return None

        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            return None
    
    def wait_for_element(self, selector: str, timeout: Optional[int] = None) -> bool:
        """
        Wait for an element to appear on the page.
        
        Args:
            selector: CSS selector for the element
            timeout: Optional timeout in milliseconds
            
        Returns:
            True if element found, False otherwise
        """
        try:
            if not self.page:
                return False
            
            wait_timeout = timeout or self.timeout
            self.page.wait_for_selector(selector, timeout=wait_timeout)
            return True
            
        except Exception as e:
            self.logger.warning(f"Element not found: {selector} - {e}")
            return False
    
    def _is_same_thread(self) -> bool:
        """Check if we're in the same thread that created the connection."""
        return self._connection_thread_id == threading.get_ident()

    def _safe_execute(self, operation: Callable, *args, **kwargs):
        """
        Safely execute a browser operation, handling thread issues.

        Args:
            operation: The operation to execute
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation

        Returns:
            Result of the operation or None if failed
        """
        try:
            if not self.is_connected():
                self.logger.warning("Browser not connected for operation")
                return None

            if self._is_same_thread():
                # Same thread, execute directly
                return operation(*args, **kwargs)
            else:
                # Different thread, return None and log warning
                self.logger.warning(f"Operation {operation.__name__} called from different thread, skipping")
                return None

        except Exception as e:
            self.logger.error(f"Error executing operation {operation.__name__}: {e}")
            if "thread" in str(e).lower() or "exited" in str(e).lower():
                self.logger.warning("Browser connection lost due to threading issue")
                self.cleanup()
            return None

    def is_connected(self) -> bool:
        """Check if browser is connected and page is available."""
        with self._lock:
            try:
                if not self.browser or not self.page:
                    return False

                # Check if we're in the same thread
                if not self._is_same_thread():
                    self.logger.warning("Browser accessed from different thread")
                    return False

                # Test if the page is still responsive
                try:
                    # Simple test to see if page is still accessible
                    self.page.url
                    return True
                except Exception as e:
                    self.logger.warning(f"Browser connection test failed: {e}")
                    return False

            except Exception as e:
                self.logger.warning(f"Connection check failed: {e}")
                return False

    def reconnect_if_needed(self, debug_port: int = 9222) -> bool:
        """
        Reconnect to browser if connection is lost.

        Args:
            debug_port: Chrome debug port

        Returns:
            True if reconnection successful
        """
        if self.is_connected():
            return True

        self.logger.info("Attempting to reconnect to browser session")
        self.cleanup()
        return self.connect_to_existing_session(debug_port)

    def cleanup(self) -> None:
        """Clean up browser resources."""
        with self._lock:
            try:
                if self.page:
                    try:
                        self.page.close()
                    except:
                        pass  # Ignore errors during cleanup
                    self.page = None

                if self.context:
                    try:
                        self.context.close()
                    except:
                        pass  # Ignore errors during cleanup
                    self.context = None

                if self.browser:
                    try:
                        self.browser.close()
                    except:
                        pass  # Ignore errors during cleanup
                    self.browser = None

                if self.playwright:
                    try:
                        self.playwright.stop()
                    except:
                        pass  # Ignore errors during cleanup
                    self.playwright = None

                # Reset thread ID
                self._connection_thread_id = None

                self.logger.info("Browser resources cleaned up")

            except Exception as e:
                self.logger.warning(f"Error during cleanup: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
