"""
Hybrid session manager that uses Selenium for login and Beautiful Soup for processing.

This module handles the JavaScript-heavy Verisk login using Selenium, then
switches to requests/Beautiful Soup for fast claim processing.
"""

import os
import time
import requests
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import LoginFailedException, CredentialError


class HybridSession:
    """Hybrid session using Selenium for login, requests for processing."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the hybrid session manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Session management
        self.requests_session = requests.Session()
        self.driver = None
        self.is_authenticated = False
        
        # URLs
        self.login_url = "https://identity.verisk.com/ui/login"
        self.xact_main_url = "https://www.xactanalysis.com"
        
        # Configure requests session
        self.requests_session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        self.timeout = self.config.get_int('browser', 'timeout', 30000) / 1000
        
        self.logger.info("Hybrid session manager initialized")
    
    def get_credentials(self) -> tuple[str, str]:
        """
        Get XactAnalysis credentials from environment variables.
        
        Returns:
            Tuple of (username, password)
            
        Raises:
            CredentialError: If credentials are not found
        """
        username = os.getenv('XACT_USERNAME')
        password = os.getenv('XACT_PASSWORD')
        
        if not username or not password:
            raise CredentialError(
                "XactAnalysis credentials not found. Please set XACT_USERNAME and XACT_PASSWORD environment variables."
            )
        
        return username, password
    
    def _setup_driver(self) -> webdriver.Chrome:
        """
        Set up Chrome driver for login.
        
        Returns:
            Configured Chrome driver
        """
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # Run headless for automation
        chrome_options.add_argument('--headless')
        
        # Disable images and CSS for faster loading
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(self.timeout)
            return driver
        except Exception as e:
            self.logger.error(f"Failed to setup Chrome driver: {e}")
            raise LoginFailedException(f"Failed to setup browser: {e}")
    
    def login(self) -> bool:
        """
        Login using Selenium, then transfer session to requests.
        
        Returns:
            True if login successful
            
        Raises:
            LoginFailedException: If login fails
        """
        try:
            self.logger.info("Starting hybrid login process")
            
            # Get credentials
            username, password = self.get_credentials()
            
            # Setup browser
            self.logger.debug("Setting up Chrome driver...")
            self.driver = self._setup_driver()
            
            # Navigate to login page
            self.logger.debug("Navigating to login page...")
            self.driver.get(self.login_url)
            
            # Wait for login form to load
            self.logger.debug("Waiting for login form...")
            wait = WebDriverWait(self.driver, self.timeout)
            
            # Try different selectors for username field
            username_selectors = [
                'input[type="email"]',
                'input[name="username"]',
                'input[name="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="username"]',
                '#username',
                '#email'
            ]
            
            username_field = None
            for selector in username_selectors:
                try:
                    username_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    self.logger.debug(f"Found username field with selector: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not username_field:
                raise LoginFailedException("Could not find username field")
            
            # Enter username
            self.logger.debug("Entering username...")
            username_field.clear()
            username_field.send_keys(username)
            
            # Find password field
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                '#password'
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not password_field:
                raise LoginFailedException("Could not find password field")
            
            # Enter password
            self.logger.debug("Entering password...")
            password_field.clear()
            password_field.send_keys(password)
            
            # Find and click login button
            login_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Sign In")',
                'button:contains("Login")',
                '.login-button',
                '#login-button'
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not login_button:
                raise LoginFailedException("Could not find login button")
            
            # Click login
            self.logger.debug("Clicking login button...")
            login_button.click()
            
            # Wait for redirect to XactAnalysis
            self.logger.debug("Waiting for login completion...")
            try:
                wait.until(lambda driver: 'xactanalysis.com' in driver.current_url)
                self.logger.info("Login successful - redirected to XactAnalysis")
            except TimeoutException:
                # Check if we're still on login page (indicates failure)
                if 'identity.verisk.com' in self.driver.current_url:
                    raise LoginFailedException("Login failed - still on login page")
            
            # Transfer cookies to requests session
            self.logger.debug("Transferring session cookies...")
            selenium_cookies = self.driver.get_cookies()
            
            for cookie in selenium_cookies:
                self.requests_session.cookies.set(
                    cookie['name'],
                    cookie['value'],
                    domain=cookie.get('domain'),
                    path=cookie.get('path', '/'),
                    secure=cookie.get('secure', False)
                )
            
            self.is_authenticated = True
            self.logger.info("Hybrid login completed successfully")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Hybrid login failed: {e}")
            raise LoginFailedException(f"Hybrid login failed: {e}")
        finally:
            # Clean up browser
            if self.driver:
                self.driver.quit()
                self.driver = None
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """
        Make authenticated GET request using requests session.
        
        Args:
            url: URL to request
            **kwargs: Additional arguments
            
        Returns:
            Response object
        """
        if not self.is_authenticated:
            raise LoginFailedException("Not authenticated. Please login first.")
        
        # Ensure URL is absolute
        if not url.startswith('http'):
            if url.startswith('/'):
                url = f"{self.xact_main_url}{url}"
            else:
                url = f"{self.xact_main_url}/{url}"
        
        response = self.requests_session.get(url, timeout=self.timeout, **kwargs)
        response.raise_for_status()
        return response
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """
        Make authenticated POST request using requests session.
        
        Args:
            url: URL to request
            **kwargs: Additional arguments
            
        Returns:
            Response object
        """
        if not self.is_authenticated:
            raise LoginFailedException("Not authenticated. Please login first.")
        
        # Ensure URL is absolute
        if not url.startswith('http'):
            if url.startswith('/'):
                url = f"{self.xact_main_url}{url}"
            else:
                url = f"{self.xact_main_url}/{url}"
        
        response = self.requests_session.post(url, timeout=self.timeout, **kwargs)
        response.raise_for_status()
        return response
    
    def close(self):
        """Close sessions and clean up resources."""
        if self.requests_session:
            self.requests_session.close()
        if self.driver:
            self.driver.quit()
            self.driver = None
        self.is_authenticated = False
        self.logger.info("Hybrid session closed")
