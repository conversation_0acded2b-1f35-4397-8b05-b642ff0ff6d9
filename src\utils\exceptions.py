"""
Custom exceptions for XactAnalysis automation.

This module defines specific exception types for different error scenarios
to enable targeted error handling and recovery strategies.
"""


class XAExtractionError(Exception):
    """Base exception for XactAnalysis extraction errors."""
    pass


class ConfigurationError(XAExtractionError):
    """Raised when there's an issue with configuration."""
    pass


class CredentialError(XAExtractionError):
    """Raised when there's an issue with credentials."""
    pass


class LoginFailedException(XAExtractionError):
    """Raised when login to XactAnalysis fails."""
    pass


class ClaimNotFoundException(XAExtractionError):
    """Raised when a claim number is not found in XactAnalysis."""
    pass


class NavigationError(XAExtractionError):
    """Raised when browser navigation fails."""
    pass


class PDFGenerationError(XAExtractionError):
    """Raised when PDF generation fails."""
    pass


class ExcelProcessingError(XAExtractionError):
    """Raised when Excel file processing fails."""
    pass


class BrowserError(XAExtractionError):
    """Raised when browser automation encounters an error."""
    pass


class TimeoutError(XAExtractionError):
    """Raised when operations timeout."""
    pass


class RetryExhaustedError(XAExtractionError):
    """Raised when maximum retry attempts are exceeded."""
    pass


class FileOperationError(XAExtractionError):
    """Raised when file operations fail."""
    pass


class ValidationError(XAExtractionError):
    """Raised when data validation fails."""
    pass
