#!/usr/bin/env python3
"""
Explore XactAnalysis application structure to find working search endpoints.
"""

import sys
import os
import json
import requests
from bs4 import BeautifulSoup

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def explore_xact_structure():
    """Explore XactAnalysis structure to find working endpoints."""
    
    try:
        from src.automation_bs4.manual_session import ManualSession
        from src.utils.config_manager import ConfigManager
        
        print("=== Exploring XactAnalysis Application Structure ===")
        
        # Load fresh cookies
        with open('fresh_cookies_formatted.json', 'r') as f:
            fresh_cookies = f.read()
        
        config = ConfigManager()
        manual_session = ManualSession(config)
        
        # Load fresh cookies
        print("📋 Loading fresh cookies...")
        result = manual_session.load_cookies_from_text(fresh_cookies)
        
        if not result['success']:
            print(f"❌ Failed to load cookies: {result['message']}")
            return
        
        print("✅ Cookies loaded successfully")
        
        # Try various XactAnalysis paths to find working endpoints
        test_paths = [
            '/apps',
            '/apps/main',
            '/apps/main/main.do',
            '/apps/xactanalysis',
            '/xactanalysis',
            '/xactanalysis/main.jsp',
            '/xactanalysis/index.jsp',
            '/main.jsp',
            '/index.jsp',
            '/search.jsp',
            '/claims',
            '/claims/search',
            '/api',
            '/api/search'
        ]
        
        working_endpoints = []
        
        for path in test_paths:
            url = f"https://www.xactanalysis.com{path}"
            print(f"\n🌐 Testing: {url}")
            
            try:
                response = manual_session.session.get(url, timeout=10, allow_redirects=True)
                print(f"  📊 Status: {response.status_code}")
                print(f"  🔗 Final URL: {response.url}")
                
                if response.status_code == 200:
                    working_endpoints.append({
                        'path': path,
                        'url': url,
                        'final_url': response.url,
                        'content_length': len(response.content)
                    })
                    
                    # Parse content to look for search functionality
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Look for forms
                    forms = soup.find_all('form')
                    if forms:
                        print(f"  📝 Found {len(forms)} forms")
                        for i, form in enumerate(forms[:2]):  # Show first 2
                            action = form.get('action', 'No action')
                            method = form.get('method', 'GET')
                            inputs = form.find_all('input')
                            print(f"    Form {i+1}: {method} {action} ({len(inputs)} inputs)")
                    
                    # Look for search-related elements
                    search_elements = soup.find_all(['input', 'button', 'a'], 
                                                  string=lambda text: text and 'search' in text.lower())
                    if search_elements:
                        print(f"  🔍 Found {len(search_elements)} search-related elements")
                    
                    # Look for claim-related elements
                    claim_elements = soup.find_all(['input', 'button', 'a'], 
                                                 string=lambda text: text and 'claim' in text.lower())
                    if claim_elements:
                        print(f"  📋 Found {len(claim_elements)} claim-related elements")
                    
                    # Save working pages for inspection
                    filename = f"working_page_{path.replace('/', '_').replace('.', '_')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"  💾 Saved to {filename}")
                
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        print(f"\n✅ Found {len(working_endpoints)} working endpoints:")
        for endpoint in working_endpoints:
            print(f"  • {endpoint['path']} -> {endpoint['final_url']} ({endpoint['content_length']} bytes)")
        
        # Try to follow the routing redirect manually
        print(f"\n🔄 Following routing redirect manually...")
        try:
            routing_response = manual_session.session.get(
                'https://www.xactanalysis.com/apps/routing/routeUser.do', 
                allow_redirects=False
            )
            print(f"Routing response status: {routing_response.status_code}")
            
            if 'Location' in routing_response.headers:
                redirect_location = routing_response.headers['Location']
                print(f"Redirect location: {redirect_location}")
                
                # Try to access the redirect location
                if redirect_location.startswith('/'):
                    redirect_url = f"https://www.xactanalysis.com{redirect_location}"
                else:
                    redirect_url = redirect_location
                
                print(f"Trying redirect URL: {redirect_url}")
                redirect_response = manual_session.session.get(redirect_url, allow_redirects=True)
                print(f"Redirect response status: {redirect_response.status_code}")
                print(f"Redirect final URL: {redirect_response.url}")
                
                if redirect_response.status_code == 200:
                    with open('redirect_page.html', 'w', encoding='utf-8') as f:
                        f.write(redirect_response.text)
                    print("💾 Saved redirect page to redirect_page.html")
            
        except Exception as e:
            print(f"❌ Routing redirect error: {e}")
        
        manual_session.close()
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    explore_xact_structure()
