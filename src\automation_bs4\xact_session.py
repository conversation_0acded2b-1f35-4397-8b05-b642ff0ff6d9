"""
XactAnalysis session manager using requests and Beautiful Soup.

This module handles authentication, session management, and HTTP requests
to XactAnalysis without requiring browser automation.
"""

import os
import time
import requests
from typing import Optional, Dict, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import LoginFailedException, CredentialError, NavigationError


class XactSession:
    """Manages authenticated session with XactAnalysis using requests."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the XactAnalysis session manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Session management
        self.session = requests.Session()
        self.is_authenticated = False
        self.base_url = self.config.get('xactanalysis', 'base_url', 'https://identity.verisk.com')
        self.xact_main_url = 'https://www.xactanalysis.com'
        
        # Configure session
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Request timeouts
        self.timeout = self.config.get_int('browser', 'timeout', 30000) / 1000  # Convert to seconds
        
        self.logger.info("XactAnalysis session manager initialized")
    
    def get_credentials(self) -> tuple[str, str]:
        """
        Get XactAnalysis credentials from environment variables.
        
        Returns:
            Tuple of (username, password)
            
        Raises:
            CredentialError: If credentials are not found
        """
        username = os.getenv('XACT_USERNAME')
        password = os.getenv('XACT_PASSWORD')
        
        if not username or not password:
            raise CredentialError(
                "XactAnalysis credentials not found. Please set XACT_USERNAME and XACT_PASSWORD environment variables."
            )
        
        return username, password
    
    def login(self) -> bool:
        """
        Authenticate with XactAnalysis using Verisk Identity System.

        Returns:
            True if login successful, False otherwise

        Raises:
            LoginFailedException: If login fails
            CredentialError: If credentials are invalid
        """
        try:
            self.logger.info("Starting XactAnalysis login process via Verisk Identity")

            # Get credentials
            username, password = self.get_credentials()

            # Step 1: Start with the XactAnalysis main page to get redirected to login
            self.logger.debug("Getting XactAnalysis main page...")
            main_response = self.session.get(self.xact_main_url, timeout=self.timeout, allow_redirects=True)
            main_response.raise_for_status()

            # Step 2: If we're not redirected to login, try the login URL directly
            if 'identity.verisk.com' not in main_response.url:
                self.logger.debug("Not redirected to login, trying login URL directly...")
                login_url = f"{self.base_url}/ui/login"
                login_response = self.session.get(login_url, timeout=self.timeout)
                login_response.raise_for_status()
            else:
                login_response = main_response

            soup = BeautifulSoup(login_response.content, 'html.parser')

            # Step 3: Find the login form
            login_form = soup.find('form')
            if not login_form:
                # Try to find forms with common login indicators
                forms = soup.find_all('form')
                for form in forms:
                    if any(input_field.get('type') == 'password' for input_field in form.find_all('input')):
                        login_form = form
                        break

                if not login_form:
                    raise LoginFailedException("Could not find login form on the page")

            # Step 4: Extract form action and method
            form_action = login_form.get('action', '')
            form_method = login_form.get('method', 'post').lower()

            # Build the full URL for form submission
            if form_action.startswith('http'):
                login_url = form_action
            elif form_action.startswith('/'):
                login_url = f"{self.base_url}{form_action}"
            else:
                login_url = urljoin(login_response.url, form_action)

            # Step 5: Prepare form data
            form_data = {}

            # Find all input fields and their values
            for input_field in login_form.find_all('input'):
                field_name = input_field.get('name')
                field_type = input_field.get('type', '').lower()
                field_value = input_field.get('value', '')

                if not field_name:
                    continue

                # Handle different input types for Verisk login
                if (field_type in ['email', 'text'] or
                    'email' in field_name.lower() or
                    'user' in field_name.lower() or
                    'username' in field_name.lower()):
                    form_data[field_name] = username
                elif field_type == 'password' or 'password' in field_name.lower():
                    form_data[field_name] = password
                elif field_type == 'hidden':
                    form_data[field_name] = field_value
                elif field_type == 'checkbox' and 'remember' in field_name.lower():
                    # Don't check remember me
                    form_data[field_name] = 'false'
                else:
                    # Include other fields with their default values
                    if field_value:
                        form_data[field_name] = field_value

            self.logger.debug(f"Login URL: {login_url}")
            self.logger.debug(f"Form data keys: {list(form_data.keys())}")

            # Step 6: Submit the login form
            auth_response = self.session.post(login_url, data=form_data, timeout=self.timeout, allow_redirects=True)
            auth_response.raise_for_status()

            # Step 7: Check if login was successful
            if self._check_verisk_login_success(auth_response):
                self.is_authenticated = True
                self.logger.info("Successfully logged into XactAnalysis via Verisk Identity")
                return True
            else:
                raise LoginFailedException("Login failed - invalid credentials or unexpected response")

        except requests.RequestException as e:
            self.logger.error(f"Network error during login: {e}")
            raise LoginFailedException(f"Network error during login: {e}")
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            raise LoginFailedException(f"Login failed: {e}")
    
    def _check_login_success(self, response: requests.Response) -> bool:
        """
        Check if login was successful by analyzing the response.
        
        Args:
            response: The response from the login attempt
            
        Returns:
            True if login appears successful
        """
        # Check for common indicators of successful login
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for indicators of successful login
        success_indicators = [
            # Look for dashboard/main page elements
            soup.find(text=lambda text: text and 'dashboard' in text.lower()),
            soup.find(text=lambda text: text and 'welcome' in text.lower()),
            soup.find(text=lambda text: text and 'logout' in text.lower()),
            soup.find('a', href=lambda href: href and 'logout' in href.lower()),
            soup.find('button', text=lambda text: text and 'logout' in text.lower()),
            # Look for navigation elements that appear after login
            soup.find('nav'),
            soup.find(class_=lambda cls: cls and any(word in str(cls).lower() for word in ['nav', 'menu', 'header'])),
        ]
        
        # Look for error indicators
        error_indicators = [
            soup.find(text=lambda text: text and any(word in text.lower() for word in ['error', 'invalid', 'incorrect', 'failed'])),
            soup.find(class_=lambda cls: cls and 'error' in str(cls).lower()),
            soup.find('div', {'class': lambda cls: cls and 'alert' in str(cls).lower()}),
        ]
        
        # If we find error indicators, login likely failed
        if any(error_indicators):
            self.logger.warning("Found error indicators in login response")
            return False
        
        # If we find success indicators, login likely succeeded
        if any(success_indicators):
            self.logger.debug("Found success indicators in login response")
            return True
        
        # Check URL - if we're redirected away from login page, it might be successful
        if 'login' not in response.url.lower() and response.url != self.base_url:
            self.logger.debug("Redirected away from login page - assuming success")
            return True
        
        # Default to checking response status
        return response.status_code == 200

    def _check_verisk_login_success(self, response: requests.Response) -> bool:
        """
        Check if Verisk Identity login was successful.

        Args:
            response: The response from the login attempt

        Returns:
            True if login appears successful
        """
        # Check if we've been redirected to XactAnalysis main site
        if 'xactanalysis.com' in response.url:
            self.logger.debug("Redirected to XactAnalysis - login likely successful")
            return True

        # Check for OAuth/OIDC success indicators
        soup = BeautifulSoup(response.content, 'html.parser')

        # Look for success indicators specific to Verisk/XactAnalysis
        success_indicators = [
            # OAuth success page
            soup.find(text=lambda text: text and 'success' in text.lower()),
            soup.find(text=lambda text: text and 'authorized' in text.lower()),
            soup.find(text=lambda text: text and 'redirecting' in text.lower()),
            # XactAnalysis specific elements
            soup.find(text=lambda text: text and 'xactanalysis' in text.lower()),
            soup.find(text=lambda text: text and 'dashboard' in text.lower()),
            soup.find(text=lambda text: text and 'welcome' in text.lower()),
            # Navigation elements that appear after login
            soup.find('nav'),
            soup.find(class_=lambda cls: cls and 'nav' in str(cls).lower()),
        ]

        # Look for error indicators
        error_indicators = [
            soup.find(text=lambda text: text and any(word in text.lower()
                     for word in ['error', 'invalid', 'incorrect', 'failed', 'denied'])),
            soup.find(class_=lambda cls: cls and 'error' in str(cls).lower()),
            soup.find('div', {'class': lambda cls: cls and 'alert' in str(cls).lower()}),
            # Verisk specific error indicators
            soup.find(text=lambda text: text and 'authentication failed' in text.lower()),
        ]

        # If we find error indicators, login failed
        if any(error_indicators):
            self.logger.warning("Found error indicators in Verisk login response")
            return False

        # If we find success indicators, login succeeded
        if any(success_indicators):
            self.logger.debug("Found success indicators in Verisk login response")
            return True

        # Check if we're still on the login page (indicates failure)
        if 'login' in response.url.lower() and 'identity.verisk.com' in response.url:
            self.logger.warning("Still on Verisk login page - login likely failed")
            return False

        # Default to success if we can't determine otherwise
        return response.status_code == 200
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """
        Make an authenticated GET request.
        
        Args:
            url: URL to request
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            NavigationError: If request fails
        """
        if not self.is_authenticated:
            raise NavigationError("Not authenticated. Please login first.")
        
        try:
            # Ensure URL is absolute - use XactAnalysis main site for navigation
            if not url.startswith('http'):
                if url.startswith('/'):
                    url = f"{self.xact_main_url}{url}"
                else:
                    url = f"{self.xact_main_url}/{url}"

            response = self.session.get(url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
            
        except requests.RequestException as e:
            self.logger.error(f"GET request failed for {url}: {e}")
            raise NavigationError(f"GET request failed: {e}")
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """
        Make an authenticated POST request.
        
        Args:
            url: URL to request
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            NavigationError: If request fails
        """
        if not self.is_authenticated:
            raise NavigationError("Not authenticated. Please login first.")
        
        try:
            # Ensure URL is absolute - use XactAnalysis main site for navigation
            if not url.startswith('http'):
                if url.startswith('/'):
                    url = f"{self.xact_main_url}{url}"
                else:
                    url = f"{self.xact_main_url}/{url}"

            response = self.session.post(url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
            
        except requests.RequestException as e:
            self.logger.error(f"POST request failed for {url}: {e}")
            raise NavigationError(f"POST request failed: {e}")
    
    def close(self):
        """Close the session and clean up resources."""
        if self.session:
            self.session.close()
        self.is_authenticated = False
        self.logger.info("XactAnalysis session closed")
