2025-06-16 20:05:59,610 - src.web_app - ERROR - Error loading dashboard: index.html
2025-06-16 20:05:59,610 - src.web_app - ERROR - Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\XA Extraction\src\web_app.py", line 49, in index
    return render_template('index.html',
                         claims_summary=claims_summary,
                         system_status=system_status)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: index.html

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\XA Extraction\src\web_app.py", line 54, in index
    return render_template('error.html', error=str(e))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: error.html
2025-06-16 20:57:31,511 - src.web_app - INFO - Attempting to connect to browser on port 9222
2025-06-16 20:57:32,166 - src.web_app - INFO - Successfully connected to browser session
2025-06-16 20:57:42,745 - src.web_app - INFO - Processing next claim: **********
2025-06-16 20:57:42,745 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 20:57:42,762 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:09,212 - src.web_app - INFO - Processing next claim: **********
2025-06-16 20:58:09,212 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 20:58:09,227 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:47,284 - src.web_app - INFO - Processing next claim: **********
2025-06-16 20:58:47,285 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 20:58:47,290 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:49,808 - src.web_app - INFO - Processing next claim: **********
2025-06-16 20:58:49,808 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 20:58:49,815 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:59:10,936 - src.web_app - INFO - Processing next claim: **********
2025-06-16 20:59:10,936 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 20:59:10,941 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:02:05,792 - src.web_app - ERROR - Error loading dashboard: index.html
2025-06-16 21:02:05,792 - src.web_app - ERROR - Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\XA Extraction\src\web_app.py", line 53, in index
    return render_template('index.html',
                         claims_summary=claims_summary,
                         system_status=system_status)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: index.html

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\XA Extraction\src\web_app.py", line 58, in index
    return render_template('error.html', error=str(e))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: error.html
2025-06-16 21:03:55,458 - src.web_app - INFO - Attempting to connect to browser on port 9222
2025-06-16 21:03:56,068 - src.web_app - INFO - Successfully connected to browser session
2025-06-16 21:04:02,927 - src.web_app - INFO - Processing next claim: **********
2025-06-16 21:04:02,927 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 21:04:02,935 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:10,185 - src.web_app - INFO - Processing next claim: **********
2025-06-16 21:04:10,185 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 21:04:10,191 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:37,686 - src.web_app - INFO - Processing next claim: **********
2025-06-16 21:04:37,686 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 21:04:37,693 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:39,643 - src.web_app - INFO - Processing next claim: **********
2025-06-16 21:04:39,643 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 21:04:39,650 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:14:03,806 - src.web_app - INFO - Processing next claim: **********
2025-06-16 21:14:03,806 - src.web_app - INFO - Step 1: Searching for claim **********
2025-06-16 21:14:03,813 - src.web_app - ERROR - Error processing claim **********: Search failed for claim **********: cannot switch to a different thread (which happens to have exited)
2025-06-17 07:30:11,997 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://identity.verisk.com/connect/authorize?ui_locales=en-US&scope=openid+claims.services.attachment.api+offline_access+xm8.services.internal.api+xm8.services.internal.administration&response_type=code+id_token&redirect_uri=https%3A%2F%2Fwww.xactanalysis.com%2Fapps%2Frouting%2FrouteUser.do&state=%7B%22session_override%22%3A%22N%22%2C%22fast_route%22%3A%22%22%2C%22detail_tab%22%3A%22d_assignment%22%2C%22detail_mfn%22%3A%22%22%2C%22context%22%3A%22GENER%22%7D&nonce=eH525dicXurqLlY4LBnxg3trKtOVj4WI&client_id=xactanalysis.hybrid&response_mode=form_post', 'title': 'identity.verisk.com/connect/authorize?ui_locales=en-US&scope=openid+claims.services.attachment.api+offline_access+xm8.services.internal.api+xm8.services.internal.administration&response_type=code+id_token&redirect_uri=https%3A%2F%2Fwww.xactanalysis.com%2Fapps%2Frouting%2FrouteUser.do&state=%7B"session_override"%3A"N"%2C"fast_route"%3A""%2C"detail_tab"%3A"d_assignment"%2C"detail_mfn"%3A""%2C"context"%3A"GENER"%7D&nonce=eH525dicXurqLlY4LBnxg3trKtOVj4WI&client_id=xactanalysis.hybrid&response_mode=form_post'}
2025-06-17 07:30:11,998 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:30:12,376 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/routing/routeUser.do', 'title': 'XactAnalysis®'}
2025-06-17 07:30:12,376 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:30:13,181 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/xactanalysis/search.jsp?date_type=received&date_preset=365&xasp_status_type=in_progress&columns=cache', 'title': 'XactAnalysis® SP > Search'}
2025-06-17 07:30:13,182 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:30:59,465 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:31:01,763 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:31:02,846 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:31:18,349 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:31:19,850 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/xactanalysis/search.jsp?columns=cache&src=qs&date_type=received&date_preset=3y&quick_search=**********&dataset=all&test_assignments=All', 'title': 'XactAnalysis® SP > Search'}
2025-06-17 07:31:19,850 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:37:25,027 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:37:36,650 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:37:43,523 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/xactanalysis/search.jsp?columns=cache&src=qs&date_type=received&date_preset=3y&quick_search=**********&dataset=all&test_assignments=All', 'title': 'XactAnalysis® SP > Search'}
2025-06-17 07:37:43,524 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:37:48,633 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:37:50,144 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/xactanalysis/search.jsp?columns=cache&src=qs&date_type=received&date_preset=3y&quick_search=**********&dataset=all&test_assignments=All', 'title': 'XactAnalysis® SP > Search'}
2025-06-17 07:37:50,144 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:38:13,847 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': 1284944025, 'url': 'https://www.xactanalysis.com/apps/cxa/detail.jsp?mfn=0630WVY&src=qs#d_assignment', 'title': 'XactAnalysis® SP > Assignment Detail'}
2025-06-17 07:38:13,848 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:38:36,994 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/cxa/detail.jsp?mfn=0630WVY&src=qs&xlink=true#d_assignment', 'title': 'XactAnalysis® SP > Assignment Detail'}
2025-06-17 07:38:36,994 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:40:53,731 - src.web_app - INFO - Providing next claim to extension: **********
2025-06-17 07:40:55,242 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/xactanalysis/search.jsp?columns=cache&src=qs&date_type=received&date_preset=3y&quick_search=**********&dataset=all&test_assignments=All', 'title': 'XactAnalysis® SP > Search'}
2025-06-17 07:40:55,242 - src.web_app - INFO - XactAnalysis page loaded in browser
2025-06-17 07:41:03,068 - src.web_app - INFO - Extension event: xa_page_loaded - {'tabId': **********, 'url': 'https://www.xactanalysis.com/apps/cxa/detail.jsp?mfn=0630WVY&src=qs&xlink=true#d_assignment', 'title': 'XactAnalysis® SP > Assignment Detail'}
2025-06-17 07:41:03,068 - src.web_app - INFO - XactAnalysis page loaded in browser
