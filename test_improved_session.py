#!/usr/bin/env python3
"""
Test the improved session verification.
"""

import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_improved_session():
    """Test the improved session verification."""
    
    try:
        from src.automation_bs4.manual_session import ManualSession
        from src.utils.config_manager import ConfigManager
        
        print("=== Testing Improved Session Verification ===")
        
        config = ConfigManager()
        manual_session = ManualSession(config)
        
        # Load saved cookies
        print("📋 Loading saved cookies...")
        result = manual_session.load_saved_cookies()
        
        print(f"\n📊 Result: {json.dumps(result, indent=2)}")
        
        if result['success']:
            print("\n✅ Session verification PASSED!")
            if 'details' in result:
                print("📝 Details:")
                for detail in result['details']:
                    print(f"  • {detail}")
        else:
            print("\n❌ Session verification FAILED!")
            print(f"💬 Message: {result['message']}")
            
            if 'details' in result:
                print("📝 Failure details:")
                for detail in result['details']:
                    print(f"  • {detail}")
            
            if 'final_url' in result:
                print(f"🔗 Final URL: {result['final_url']}")
            
            if 'suggestion' in result:
                print(f"💡 Suggestion: {result['suggestion']}")
        
        manual_session.close()
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_session()
