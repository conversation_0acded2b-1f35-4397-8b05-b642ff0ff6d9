#!/usr/bin/env python3
"""
Detailed debug script to understand Excel structure discrepancy.
"""

import sys
from pathlib import Path
import pandas as pd
from openpyxl import load_workbook

def main():
    """Debug Excel file structure in detail."""
    try:
        print("Detailed Excel Structure Analysis...")
        
        excel_path = Path("Adam Example Info/Adam Jobs in 24.xlsx")
        
        # Read with openpyxl first
        print("\n1. OpenPyXL Analysis:")
        workbook = load_workbook(excel_path)
        worksheet = workbook.active
        
        print(f"Worksheet title: {worksheet.title}")
        print(f"Max row: {worksheet.max_row}")
        print(f"Max column: {worksheet.max_column}")
        
        # Show first row (headers) with openpyxl
        print("\nHeaders from OpenPyXL:")
        for col in range(1, min(worksheet.max_column + 1, 10)):
            header = worksheet.cell(row=1, column=col).value
            print(f"  Column {col}: {header}")
        
        # Show first few data rows with openpyxl
        print("\nFirst 3 data rows from OpenPyXL:")
        for row in range(2, 5):
            row_data = []
            for col in range(1, min(worksheet.max_column + 1, 10)):
                value = worksheet.cell(row=row, column=col).value
                row_data.append(str(value) if value is not None else "")
            print(f"  Row {row}: {row_data}")
        
        workbook.close()
        
        # Read with pandas
        print("\n2. Pandas Analysis:")
        df = pd.read_excel(excel_path, sheet_name=0)
        
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {list(df.columns)}")
        
        # Show first few rows with pandas
        print("\nFirst 3 rows from Pandas:")
        for i in range(min(3, len(df))):
            print(f"  Row {i}: {df.iloc[i].tolist()}")
        
        # Check if there are any hidden columns or different sheet structure
        print("\n3. Sheet Information:")
        workbook = load_workbook(excel_path)
        print(f"Sheet names: {workbook.sheetnames}")
        
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            print(f"  Sheet '{sheet_name}': {sheet.max_row} rows, {sheet.max_column} columns")
        
        workbook.close()
        
        print("\n✅ Detailed analysis completed!")
        
    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
