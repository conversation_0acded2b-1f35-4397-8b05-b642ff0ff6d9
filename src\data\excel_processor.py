"""
Excel processing module for XactAnalysis automation.

This module handles reading claim numbers from Excel files, tracking completion
status, and safely updating Excel files with completion markers.
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import ExcelProcessingError, ValidationError, FileOperationError


class ExcelProcessor:
    """Handles Excel file operations for claim processing."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the Excel processor.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.excel_file_path = self.config.excel_file_path
        self.claim_column = self.config.get_int('excel', 'claim_column', 2)  # Column C = 2
        self.status_column = self.config.get_int('excel', 'status_column', 0)  # Column A = 0
        self.completion_marker = self.config.get('excel', 'completion_marker', 'D')
        sheet_name_config = self.config.get('excel', 'sheet_name', '')
        self.sheet_name = sheet_name_config if sheet_name_config.strip() else None
        
        # Validate file exists
        if not self.excel_file_path.exists():
            raise FileOperationError(f"Excel file not found: {self.excel_file_path}")

        # Determine file type
        self.is_csv = self.excel_file_path.suffix.lower() == '.csv'
        file_type = "CSV" if self.is_csv else "Excel"

        self.logger.info(f"Excel processor initialized for {file_type} file: {self.excel_file_path}")
    
    def load_claims(self) -> List[Dict[str, Any]]:
        """
        Load claim numbers from the Excel file.
        
        Returns:
            List of dictionaries containing claim data with row indices
            
        Raises:
            ExcelProcessingError: If Excel file cannot be read
            ValidationError: If required columns are missing
        """
        try:
            self.logger.info("Loading claims from Excel file")
            
            # Read file (CSV or Excel)
            if self.is_csv:
                df = pd.read_csv(self.excel_file_path)
            else:
                if self.sheet_name:
                    df = pd.read_excel(self.excel_file_path, sheet_name=self.sheet_name)
                else:
                    # Read the first sheet explicitly
                    df = pd.read_excel(self.excel_file_path, sheet_name=0)
            
            # Validate columns exist
            if df.shape[1] <= max(self.claim_column, self.status_column):
                raise ValidationError(
                    f"Excel file doesn't have enough columns. "
                    f"Required: {max(self.claim_column, self.status_column) + 1}, "
                    f"Found: {df.shape[1]}"
                )
            
            # Extract claims
            claims = []
            for index, row in df.iterrows():
                # Get claim number (skip if empty)
                claim_number = row.iloc[self.claim_column]
                if pd.isna(claim_number) or str(claim_number).strip() == '':
                    continue
                
                # Get status (check if already completed)
                status = row.iloc[self.status_column] if self.status_column < len(row) else None
                is_completed = str(status).strip() == self.completion_marker if not pd.isna(status) else False
                
                claims.append({
                    'row_index': index,
                    'claim_number': str(claim_number).strip(),
                    'status': status,
                    'is_completed': is_completed,
                    'excel_row': index + 2  # Excel rows are 1-based, plus header
                })
            
            self.logger.info(f"Loaded {len(claims)} claims from Excel file")
            
            # Log summary
            completed_count = sum(1 for claim in claims if claim['is_completed'])
            pending_count = len(claims) - completed_count
            self.logger.info(f"Claims summary - Total: {len(claims)}, "
                           f"Completed: {completed_count}, Pending: {pending_count}")
            
            return claims
            
        except Exception as e:
            if isinstance(e, (ExcelProcessingError, ValidationError)):
                raise
            raise ExcelProcessingError(f"Failed to load claims from Excel: {e}")
    
    def get_pending_claims(self) -> List[Dict[str, Any]]:
        """
        Get only the claims that haven't been completed yet.
        
        Returns:
            List of pending claim dictionaries
        """
        all_claims = self.load_claims()
        pending_claims = [claim for claim in all_claims if not claim['is_completed']]
        
        self.logger.info(f"Found {len(pending_claims)} pending claims out of {len(all_claims)} total")
        return pending_claims
    
    def mark_claim_completed(self, claim_number: str, row_index: Optional[int] = None) -> bool:
        """
        Mark a claim as completed in the file.

        Args:
            claim_number: The claim number to mark as completed
            row_index: Optional row index for faster lookup

        Returns:
            True if successfully marked, False otherwise

        Raises:
            ExcelProcessingError: If file cannot be updated
        """
        try:
            self.logger.info(f"Marking claim {claim_number} as completed")

            # Create backup first
            backup_path = self._create_backup()

            try:
                if self.is_csv:
                    # Handle CSV file
                    df = pd.read_csv(self.excel_file_path)

                    # Find the row if row_index not provided
                    if row_index is None:
                        row_index = self._find_claim_row_csv(df, claim_number)
                        if row_index is None:
                            raise ValidationError(f"Claim number {claim_number} not found in CSV file")

                    # Update the status column
                    df.iloc[row_index, self.status_column] = self.completion_marker

                    # Save the CSV
                    df.to_csv(self.excel_file_path, index=False)

                else:
                    # Handle Excel file
                    workbook = load_workbook(self.excel_file_path)

                    # Get the worksheet
                    if self.sheet_name:
                        if self.sheet_name not in workbook.sheetnames:
                            raise ValidationError(f"Sheet '{self.sheet_name}' not found in workbook")
                        worksheet = workbook[self.sheet_name]
                    else:
                        worksheet = workbook.active

                    # Find the row if row_index not provided
                    if row_index is None:
                        row_index = self._find_claim_row(worksheet, claim_number)
                        if row_index is None:
                            raise ValidationError(f"Claim number {claim_number} not found in Excel file")

                    # Convert to Excel row number (1-based, plus header)
                    excel_row = row_index + 2

                    # Update the status column
                    status_cell = worksheet.cell(row=excel_row, column=self.status_column + 1)
                    status_cell.value = self.completion_marker

                    # Save the workbook
                    workbook.save(self.excel_file_path)
                    workbook.close()

                self.logger.info(f"Successfully marked claim {claim_number} as completed")
                return True

            except Exception as e:
                # Restore backup on error
                self._restore_backup(backup_path)
                raise ExcelProcessingError(f"Failed to update file: {e}")

            finally:
                # Clean up backup
                self._cleanup_backup(backup_path)

        except Exception as e:
            if isinstance(e, ExcelProcessingError):
                raise
            raise ExcelProcessingError(f"Failed to mark claim {claim_number} as completed: {e}")
    
    def _find_claim_row(self, worksheet, claim_number: str) -> Optional[int]:
        """Find the row index for a given claim number in Excel."""
        claim_column_letter = worksheet.cell(row=1, column=self.claim_column + 1).column_letter

        for row_num in range(2, worksheet.max_row + 1):  # Start from row 2 (skip header)
            cell_value = worksheet[f"{claim_column_letter}{row_num}"].value
            if cell_value and str(cell_value).strip() == claim_number:
                return row_num - 2  # Convert back to 0-based index

        return None

    def _find_claim_row_csv(self, df: pd.DataFrame, claim_number: str) -> Optional[int]:
        """Find the row index for a given claim number in CSV."""
        claim_column_data = df.iloc[:, self.claim_column]

        for index, value in claim_column_data.items():
            if pd.notna(value) and str(value).strip() == claim_number:
                return index

        return None
    
    def _create_backup(self) -> Path:
        """Create a backup of the Excel file."""
        backup_path = self.excel_file_path.with_suffix(f'.backup{self.excel_file_path.suffix}')
        shutil.copy2(self.excel_file_path, backup_path)
        self.logger.debug(f"Created backup: {backup_path}")
        return backup_path
    
    def _restore_backup(self, backup_path: Path) -> None:
        """Restore Excel file from backup."""
        if backup_path.exists():
            shutil.copy2(backup_path, self.excel_file_path)
            self.logger.warning(f"Restored Excel file from backup: {backup_path}")
    
    def _cleanup_backup(self, backup_path: Path) -> None:
        """Clean up backup file."""
        try:
            if backup_path.exists():
                backup_path.unlink()
                self.logger.debug(f"Cleaned up backup: {backup_path}")
        except Exception as e:
            self.logger.warning(f"Failed to clean up backup {backup_path}: {e}")
    
    def get_file_info(self) -> Dict[str, Any]:
        """
        Get information about the Excel file.
        
        Returns:
            Dictionary with file information
        """
        try:
            stat = self.excel_file_path.stat()
            
            # Load basic info
            if self.is_csv:
                df = pd.read_csv(self.excel_file_path)
            else:
                if self.sheet_name:
                    df = pd.read_excel(self.excel_file_path, sheet_name=self.sheet_name)
                else:
                    df = pd.read_excel(self.excel_file_path, sheet_name=0)
            
            return {
                'file_path': str(self.excel_file_path),
                'file_size': stat.st_size,
                'modified_time': stat.st_mtime,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'sheet_name': self.sheet_name or 'Default',
                'claim_column': self.claim_column,
                'status_column': self.status_column,
                'completion_marker': self.completion_marker
            }
            
        except Exception as e:
            raise ExcelProcessingError(f"Failed to get file info: {e}")
    
    def validate_file_structure(self) -> bool:
        """
        Validate that the Excel file has the expected structure.
        
        Returns:
            True if valid, raises exception if not
            
        Raises:
            ValidationError: If file structure is invalid
        """
        try:
            if self.is_csv:
                df = pd.read_csv(self.excel_file_path)
            else:
                if self.sheet_name:
                    df = pd.read_excel(self.excel_file_path, sheet_name=self.sheet_name)
                else:
                    df = pd.read_excel(self.excel_file_path, sheet_name=0)
            
            # Check minimum columns
            min_columns = max(self.claim_column, self.status_column) + 1
            if df.shape[1] < min_columns:
                raise ValidationError(
                    f"Excel file must have at least {min_columns} columns. Found: {df.shape[1]}"
                )
            
            # Check for at least one claim
            claim_column_data = df.iloc[:, self.claim_column].dropna()
            if len(claim_column_data) == 0:
                raise ValidationError(f"No claim numbers found in column {self.claim_column + 1}")
            
            self.logger.info("Excel file structure validation passed")
            return True
            
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(f"File structure validation failed: {e}")
