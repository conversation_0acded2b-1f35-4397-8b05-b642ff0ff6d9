#!/usr/bin/env python3
"""
Analyze the cookies to understand why session verification is failing.
"""

import json
from pathlib import Path
from datetime import datetime

def analyze_cookies():
    """Analyze the cookies in session_cookies.json."""
    
    print("=== Cookie Analysis ===")
    
    # Check if cookies file exists
    cookie_file = Path('session_cookies.json')
    if not cookie_file.exists():
        print("❌ No session_cookies.json file found")
        return
    
    # Load cookies
    with open(cookie_file, 'r') as f:
        cookies_data = json.load(f)
    
    print(f"📄 Total cookies: {len(cookies_data)}")
    
    # Analyze cookies by domain
    domains = {}
    for cookie in cookies_data:
        domain = cookie.get('domain', 'unknown')
        if domain not in domains:
            domains[domain] = []
        domains[domain].append(cookie)
    
    print(f"\n🌐 Cookies by domain:")
    for domain, domain_cookies in domains.items():
        print(f"  {domain}: {len(domain_cookies)} cookies")
        for cookie in domain_cookies:
            name = cookie.get('name', 'unknown')
            value = cookie.get('value', '')
            secure = cookie.get('secure', False)
            print(f"    - {name}: {value[:20]}{'...' if len(value) > 20 else ''} (secure: {secure})")
    
    # Look for important authentication cookies
    important_cookies = [
        'JSESSIONID', 'userID', 'AWSALB', 'AWSALBCORS',
        '.AspNetCore.Antiforgery', 'XSRF-TOKEN'
    ]
    
    print(f"\n🔑 Important authentication cookies:")
    found_important = []
    for cookie in cookies_data:
        name = cookie.get('name', '')
        if any(important in name for important in important_cookies):
            found_important.append(cookie)
            value = cookie.get('value', '')
            domain = cookie.get('domain', '')
            secure = cookie.get('secure', False)
            print(f"  ✅ {name} ({domain}): {value[:30]}{'...' if len(value) > 30 else ''} (secure: {secure})")
    
    if not found_important:
        print("  ❌ No important authentication cookies found!")
    
    # Check for potential issues
    print(f"\n⚠️  Potential issues:")
    
    # Check for duplicate cookies
    cookie_names = {}
    for cookie in cookies_data:
        name = cookie.get('name', '')
        domain = cookie.get('domain', '')
        key = f"{name}@{domain}"
        if key in cookie_names:
            print(f"  🔄 Duplicate cookie: {name} for domain {domain}")
        else:
            cookie_names[key] = cookie
    
    # Check for mixed secure/non-secure cookies
    xact_cookies = [c for c in cookies_data if 'xactanalysis.com' in c.get('domain', '')]
    verisk_cookies = [c for c in cookies_data if 'verisk.com' in c.get('domain', '')]
    
    print(f"  📊 XactAnalysis cookies: {len(xact_cookies)}")
    print(f"  📊 Verisk cookies: {len(verisk_cookies)}")
    
    # Check for session-specific cookies that might be expired
    session_cookies = [c for c in cookies_data if 'session' in c.get('name', '').lower()]
    if session_cookies:
        print(f"  🕐 Session-related cookies: {len(session_cookies)}")
        for cookie in session_cookies:
            print(f"    - {cookie.get('name', '')}: {cookie.get('value', '')[:20]}...")
    
    # Check for timestamp-related cookies
    timestamp_cookies = [c for c in cookies_data if any(x in c.get('name', '').lower() for x in ['time', 'date', 'last'])]
    if timestamp_cookies:
        print(f"  📅 Timestamp-related cookies:")
        for cookie in timestamp_cookies:
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            print(f"    - {name}: {value}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    print("  1. The cookies appear to be redirecting to login, which suggests:")
    print("     - Cookies may be expired")
    print("     - Session may have timed out")
    print("     - Missing critical authentication tokens")
    print("  2. Try logging in manually again to get fresh cookies")
    print("  3. Make sure to capture all cookies from both xactanalysis.com and verisk.com domains")
    print("  4. Ensure you're logged in completely before extracting cookies")

if __name__ == "__main__":
    analyze_cookies()
