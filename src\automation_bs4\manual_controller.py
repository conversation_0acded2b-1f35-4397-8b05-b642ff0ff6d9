"""
Manual automation controller using manual login and Beautiful Soup processing.

This controller allows users to login manually, then uses Beautiful Soup
for fast claim processing.
"""

from typing import Optional, Dict, Any

from .manual_session import ManualSession
from .claim_navigator import ClaimNavigator
from .pdf_extractor import PDFExtractor
from .csv_processor import CSVProcessor
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger


class ManualAutomationController:
    """Manual automation controller for XactAnalysis."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the manual automation controller.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Initialize components with manual session
        self.session = ManualSession(self.config)
        self.navigator = ClaimNavigator(self.session, self.config)
        self.pdf_extractor = PDFExtractor(self.session, self.config)
        self.csv_processor = CSVProcessor(self.config)
        
        # State
        self.is_logged_in = False
        self.current_csv_file = None
        
        self.logger.info("Manual automation controller initialized")
    
    def load_csv_file(self, file_path: str) -> Dict[str, Any]:
        """Load a CSV file for processing."""
        try:
            self.logger.info(f"Loading CSV file: {file_path}")
            
            success = self.csv_processor.load_csv(file_path)
            
            if success:
                self.current_csv_file = file_path
                is_valid, issues = self.csv_processor.validate_csv_format()
                summary = self.csv_processor.get_file_summary()
                
                result = {
                    'success': True,
                    'message': 'CSV file loaded successfully',
                    'file_path': file_path,
                    'is_valid': is_valid,
                    'validation_issues': issues,
                    'summary': summary
                }
                
                if not is_valid:
                    result['message'] += f" (with {len(issues)} validation issues)"
                
                return result
            else:
                return {
                    'success': False,
                    'message': 'Failed to load CSV file',
                    'file_path': file_path
                }
                
        except Exception as e:
            self.logger.error(f"Failed to load CSV file: {e}")
            return {
                'success': False,
                'message': f'Failed to load CSV file: {str(e)}',
                'file_path': file_path
            }
    
    def start_manual_login(self) -> Dict[str, Any]:
        """Start the manual login process."""
        return self.session.start_manual_login()
    
    def extract_session(self) -> Dict[str, Any]:
        """Get instructions for manual session extraction."""
        return self.session.extract_session_from_browser()
    
    def load_cookies_from_text(self, cookies_text: str) -> Dict[str, Any]:
        """Load cookies from user-provided text."""
        result = self.session.load_cookies_from_text(cookies_text)
        if result['success']:
            self.is_logged_in = True
        return result
    
    def load_saved_session(self) -> Dict[str, Any]:
        """Load previously saved session cookies."""
        result = self.session.load_saved_cookies()
        if result['success']:
            self.is_logged_in = True
        return result
    
    def process_single_claim(self, claim_number: str) -> Dict[str, Any]:
        """Process a single claim using Beautiful Soup."""
        if not self.is_logged_in:
            return {
                'success': False,
                'message': 'Not logged in. Please complete manual login first.',
                'claim_number': claim_number
            }
        
        try:
            self.logger.info(f"Processing claim: {claim_number}")
            
            # Search for the claim
            claims = self.navigator.search_claim(claim_number)
            
            if not claims:
                return {
                    'success': False,
                    'message': f'Claim {claim_number} not found',
                    'claim_number': claim_number
                }
            
            # Process each claim result
            processed_pdfs = []
            
            for i, claim_data in enumerate(claims):
                try:
                    # Navigate to notes
                    notes_url = self.navigator.navigate_to_notes(claim_data)
                    
                    # Extract PDF
                    pdf_path = self.pdf_extractor.extract_notes_pdf(claim_number, notes_url, i)
                    
                    processed_pdfs.append({
                        'pdf_path': pdf_path,
                        'suffix_index': i,
                        'notes_url': notes_url
                    })
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process claim variant {i} for {claim_number}: {e}")
                    continue
            
            if not processed_pdfs:
                return {
                    'success': False,
                    'message': f'Failed to extract any PDFs for claim {claim_number}',
                    'claim_number': claim_number
                }
            
            # Mark claim as completed in CSV if loaded
            if self.current_csv_file:
                self.csv_processor.mark_claim_completed(claim_number)
                self.csv_processor.save_csv()
            
            return {
                'success': True,
                'message': f'Successfully processed claim {claim_number}',
                'claim_number': claim_number,
                'pdfs_generated': len(processed_pdfs),
                'pdf_paths': [pdf['pdf_path'] for pdf in processed_pdfs]
            }
            
        except Exception as e:
            self.logger.error(f"Failed to process claim {claim_number}: {e}")
            return {
                'success': False,
                'message': f'Failed to process claim {claim_number}: {str(e)}',
                'claim_number': claim_number
            }
    
    def process_next_claim(self) -> Dict[str, Any]:
        """Process the next pending claim from the CSV file."""
        if not self.current_csv_file:
            return {
                'success': False,
                'message': 'No CSV file loaded'
            }
        
        next_claim = self.csv_processor.get_next_pending_claim()
        
        if not next_claim:
            return {
                'success': True,
                'message': 'No pending claims to process',
                'completed': True
            }
        
        result = self.process_single_claim(next_claim['claim_number'])
        result['claim_info'] = next_claim
        
        return result
    
    def process_all_claims(self, max_claims: Optional[int] = None) -> Dict[str, Any]:
        """Process all pending claims from the CSV file."""
        if not self.current_csv_file:
            return {
                'success': False,
                'message': 'No CSV file loaded'
            }
        
        if not self.is_logged_in:
            return {
                'success': False,
                'message': 'Not logged in. Please complete manual login first.'
            }
        
        pending_claims = self.csv_processor.get_pending_claims()
        
        if not pending_claims:
            return {
                'success': True,
                'message': 'No pending claims to process',
                'completed': True,
                'processed_count': 0,
                'failed_count': 0
            }
        
        if max_claims:
            pending_claims = pending_claims[:max_claims]
        
        self.logger.info(f"Starting batch processing of {len(pending_claims)} claims")
        
        processed_count = 0
        failed_count = 0
        failed_claims = []
        
        for claim in pending_claims:
            claim_number = claim['claim_number']
            
            try:
                result = self.process_single_claim(claim_number)
                
                if result['success']:
                    processed_count += 1
                    self.logger.info(f"Successfully processed claim {claim_number} ({processed_count}/{len(pending_claims)})")
                else:
                    failed_count += 1
                    failed_claims.append({
                        'claim_number': claim_number,
                        'error': result['message']
                    })
                    self.logger.warning(f"Failed to process claim {claim_number}: {result['message']}")
                
                # Add delay between claims
                import time
                time.sleep(1)
                
            except Exception as e:
                failed_count += 1
                failed_claims.append({
                    'claim_number': claim_number,
                    'error': str(e)
                })
                self.logger.error(f"Unexpected error processing claim {claim_number}: {e}")
                continue
        
        return {
            'success': True,
            'message': f'Batch processing completed: {processed_count} successful, {failed_count} failed',
            'processed_count': processed_count,
            'failed_count': failed_count,
            'failed_claims': failed_claims,
            'total_claims': len(pending_claims)
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the manual automation system."""
        status = {
            'is_logged_in': self.is_logged_in,
            'current_csv_file': self.current_csv_file,
            'csv_summary': None,
            'method': 'manual'
        }
        
        if self.current_csv_file:
            status['csv_summary'] = self.csv_processor.get_file_summary()
        
        return status
    
    def cleanup(self):
        """Clean up resources and close connections."""
        try:
            if self.session:
                self.session.close()
            self.is_logged_in = False
            self.logger.info("Manual automation controller cleaned up")
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")
