"""
Logging configuration for XactAnalysis automation.

This module provides comprehensive logging functionality with file rotation,
colored console output, and configurable log levels.
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional
import colorlog

from .config_manager import ConfigManager


class Logger:
    """Enhanced logger with file rotation and colored console output."""
    
    def __init__(self, name: str, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the logger.
        
        Args:
            name: Logger name
            config_manager: Configuration manager instance
        """
        self.name = name
        self.config = config_manager or ConfigManager()
        self.logger = logging.getLogger(name)
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_logger()
    
    def _setup_logger(self) -> None:
        """Set up the logger with file and console handlers."""
        # Get configuration
        log_level = self.config.get('logging', 'log_level', 'INFO')
        log_format = self.config.get('logging', 'log_format', 
                                   '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        max_log_size = self.config.get_int('logging', 'max_log_size', 10) * 1024 * 1024  # MB to bytes
        backup_count = self.config.get_int('logging', 'backup_count', 5)
        
        # Set log level
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Ensure log directory exists
        log_dir = self.config.log_dir
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # File handler with rotation
        log_file = log_dir / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_log_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(log_format)
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(logging.DEBUG)  # File gets all messages
        
        # Console handler with colors
        console_handler = colorlog.StreamHandler()
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, claim_number: Optional[str] = None) -> None:
        """Log debug message."""
        self._log_with_claim(logging.DEBUG, message, claim_number)
    
    def info(self, message: str, claim_number: Optional[str] = None) -> None:
        """Log info message."""
        self._log_with_claim(logging.INFO, message, claim_number)
    
    def warning(self, message: str, claim_number: Optional[str] = None) -> None:
        """Log warning message."""
        self._log_with_claim(logging.WARNING, message, claim_number)
    
    def error(self, message: str, claim_number: Optional[str] = None, exc_info: bool = False) -> None:
        """Log error message."""
        self._log_with_claim(logging.ERROR, message, claim_number, exc_info=exc_info)
    
    def critical(self, message: str, claim_number: Optional[str] = None, exc_info: bool = False) -> None:
        """Log critical message."""
        self._log_with_claim(logging.CRITICAL, message, claim_number, exc_info=exc_info)
    
    def _log_with_claim(self, level: int, message: str, claim_number: Optional[str] = None, 
                       exc_info: bool = False) -> None:
        """Log message with optional claim number context."""
        if claim_number:
            message = f"[Claim: {claim_number}] {message}"
        
        self.logger.log(level, message, exc_info=exc_info)
    
    def log_operation_start(self, operation: str, claim_number: Optional[str] = None) -> None:
        """Log the start of an operation."""
        self.info(f"Starting {operation}", claim_number)
    
    def log_operation_success(self, operation: str, claim_number: Optional[str] = None) -> None:
        """Log successful completion of an operation."""
        self.info(f"Successfully completed {operation}", claim_number)
    
    def log_operation_failure(self, operation: str, error: Exception, 
                            claim_number: Optional[str] = None) -> None:
        """Log failure of an operation."""
        self.error(f"Failed {operation}: {error}", claim_number, exc_info=True)
    
    def log_retry_attempt(self, operation: str, attempt: int, max_attempts: int,
                         claim_number: Optional[str] = None) -> None:
        """Log retry attempt."""
        self.warning(f"Retrying {operation} (attempt {attempt}/{max_attempts})", claim_number)
    
    def log_claim_processing_start(self, claim_number: str, total_claims: int, current_index: int) -> None:
        """Log the start of claim processing."""
        self.info(f"Processing claim {current_index + 1}/{total_claims}: {claim_number}")
    
    def log_claim_processing_complete(self, claim_number: str, pdf_files: list) -> None:
        """Log successful claim processing."""
        files_str = ", ".join(pdf_files) if pdf_files else "No files"
        self.info(f"Completed processing. Generated files: {files_str}", claim_number)
    
    def log_session_start(self, total_claims: int) -> None:
        """Log the start of a processing session."""
        self.info(f"Starting XactAnalysis extraction session with {total_claims} claims")
    
    def log_session_complete(self, processed: int, successful: int, failed: int) -> None:
        """Log the completion of a processing session."""
        self.info(f"Session complete. Processed: {processed}, Successful: {successful}, Failed: {failed}")


def get_logger(name: str, config_manager: Optional[ConfigManager] = None) -> Logger:
    """
    Get a logger instance.
    
    Args:
        name: Logger name
        config_manager: Configuration manager instance
        
    Returns:
        Logger instance
    """
    return Logger(name, config_manager)
