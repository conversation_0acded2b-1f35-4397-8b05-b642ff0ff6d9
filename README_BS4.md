# XactAnalysis Automated Processor (Beautiful Soup Edition)

A fully automated solution for extracting claim notes from XactAnalysis using Beautiful Soup and requests. No browser setup required!

## 🌟 Features

- **Fully Automated**: No manual browser setup or interaction needed
- **Simple CSV Upload**: Just upload your CSV file and go
- **Automatic Login**: Handles XactAnalysis authentication automatically
- **Smart Date Range**: Automatically searches last 3 years of claims
- **Smart PDF Generation**: Automatically finds and downloads/generates PDFs
- **Progress Tracking**: Real-time progress updates and completion status
- **Error Handling**: Robust error handling with detailed logging
- **Multiple Claims**: Handles multiple entries per claim number (A, B, C suffixes)

## 🚀 Quick Start

### Option 1: Instant Demo (Recommended)

For immediate testing with pre-configured test credentials:

```bash
python quick_start.py
```

This will:
- Set up test credentials automatically
- Create a sample CSV file
- Start the web interface
- Open your browser automatically

### Option 2: Full Setup

Run the setup script to configure everything:

```bash
python setup_bs4.py
```

This will:
- Create required directories
- Set up your XactAnalysis credentials
- Install dependencies
- Test the configuration

### 2. Set Credentials

The setup script will ask for your XactAnalysis credentials, or you can set them manually:

```bash
# Create .env file from example
cp .env_bs4.example .env

# Edit .env and add your credentials
XACT_USERNAME=your_username
XACT_PASSWORD=your_password
```

**Test Credentials Available**: The system comes pre-configured with test credentials (`<EMAIL>` / `Nubilt2025!`) for immediate testing.

### 3. Run the Application

```bash
python run_bs4_app.py
```

The web interface will open automatically at http://127.0.0.1:5001

### 4. Process Claims

1. Upload your CSV file (claim numbers should be in column B)
2. Click "Login to XactAnalysis"
3. Process claims individually or all at once
4. PDFs are automatically saved to the `output` directory
5. Completed claims are marked with 'D' in column A

## 📁 Project Structure

```
XA Extraction/
├── src/automation_bs4/          # Beautiful Soup automation modules
│   ├── xact_session.py          # Session management and login
│   ├── claim_navigator.py       # Claim search and navigation
│   ├── pdf_extractor.py         # PDF extraction and generation
│   ├── csv_processor.py         # CSV file processing
│   ├── automation_controller.py # Main automation controller
│   └── web_app.py              # Web interface
├── templates_bs4/               # Web interface templates
├── config/                      # Configuration files
├── output/                      # Generated PDF files
├── logs/                        # Application logs
├── uploads/                     # Uploaded CSV files
├── run_bs4_app.py              # Main application launcher
├── setup_bs4.py               # Setup script
└── README_BS4.md              # This file
```

## ⚙️ Configuration

The system uses `config/config_bs4.ini` for configuration. Key settings:

```ini
[csv]
claim_column = 1        # Column B (0-based index)
status_column = 0       # Column A (0-based index)
completion_marker = D   # Mark for completed claims

[xactanalysis]
base_url = https://g1.xactanalysis.com
timeout = 30
search_delay = 2        # Delay between searches

[pdf]
filename_template = {claim_number}{suffix}.pdf
multiple_suffixes = _A,_B,_C,_D,_E,_F,_G,_H,_I,_J
```

## 🔧 How It Works

1. **Authentication**: Uses requests and Beautiful Soup to log into XactAnalysis
2. **Claim Search**: Automatically searches for claim numbers with "last 3 years" date range
3. **Navigation**: Finds and navigates to the notes section
4. **PDF Extraction**: Multiple methods to extract/generate PDFs:
   - Direct PDF download links
   - Print/export functionality
   - HTML to PDF conversion (fallback)
5. **Status Tracking**: Updates CSV file with completion markers

### Date Range Handling

The system automatically sets the search date range to "last 3 years" by:
- Calculating dates from 3 years ago to today
- Adding multiple date parameter formats to cover different form structures
- Detecting and setting dropdown selections for date ranges
- Using common parameter names like `dateRange=last3years`

## 📊 CSV Format

Your CSV file should have:
- **Column A**: Status (will be marked with 'D' when completed)
- **Column B**: Claim numbers
- Additional columns are preserved

Example:
```csv
Status,Claim Number,Description
,12345678,Sample claim
D,87654321,Completed claim
,11111111,Another claim
```

## 🛠️ Troubleshooting

### Common Issues

1. **Login Failed**
   - Check your credentials in the `.env` file
   - Verify XactAnalysis website is accessible
   - Check logs for detailed error messages

2. **Claims Not Found**
   - Verify claim numbers are correct
   - Check if claims exist in XactAnalysis
   - Review search functionality logs

3. **PDF Generation Failed**
   - Check if notes page is accessible
   - Verify PDF extraction methods
   - Review PDF extractor logs

### Logs

Check the `logs/` directory for detailed information:
- `src.automation_bs4.xact_session.log` - Login and session issues
- `src.automation_bs4.claim_navigator.log` - Search and navigation issues
- `src.automation_bs4.pdf_extractor.log` - PDF generation issues

### Debug Mode

Enable debug logging by setting in your `.env` file:
```bash
LOG_LEVEL=DEBUG
```

## 🔒 Security

- Credentials are stored in environment variables
- Never commit the `.env` file to version control
- Session cookies are handled securely
- All requests use HTTPS

## 🆚 Comparison with Original System

| Feature | Original (Playwright) | New (Beautiful Soup) |
|---------|----------------------|---------------------|
| Browser Setup | Manual Chrome debug setup | None required |
| Login | Manual login required | Fully automated |
| Processing | Semi-automated | Fully automated |
| Dependencies | Playwright, Chrome | requests, BeautifulSoup |
| Resource Usage | High (full browser) | Low (HTTP requests only) |
| Reliability | Browser-dependent | More stable |
| Speed | Slower (browser rendering) | Faster (direct HTTP) |

## 📝 License

Private project - All rights reserved

## 🤝 Support

For issues or questions:
1. Check the logs in the `logs/` directory
2. Review this documentation
3. Check the configuration files
4. Enable debug logging for more details
