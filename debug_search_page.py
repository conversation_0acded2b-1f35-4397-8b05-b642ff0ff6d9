#!/usr/bin/env python3
"""
Debug script to check what page the search functionality is accessing.
"""

import sys
import os
import json
import requests
from bs4 import BeautifulSoup

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_search_page():
    """Debug what page the search functionality is accessing."""
    
    try:
        from src.automation_bs4.manual_session import ManualSession
        from src.utils.config_manager import ConfigManager
        
        print("=== Debugging Search Page Access ===")
        
        # Load fresh cookies
        with open('fresh_cookies_formatted.json', 'r') as f:
            fresh_cookies = f.read()
        
        config = ConfigManager()
        manual_session = ManualSession(config)
        
        # Load fresh cookies
        print("📋 Loading fresh cookies...")
        result = manual_session.load_cookies_from_text(fresh_cookies)
        
        if not result['success']:
            print(f"❌ Failed to load cookies: {result['message']}")
            return
        
        print("✅ Cookies loaded successfully")
        
        # Test different XactAnalysis URLs
        test_urls = [
            'https://www.xactanalysis.com',
            'https://www.xactanalysis.com/apps/routing/routeUser.do',
            'https://www.xactanalysis.com/xactanalysis/search.jsp',
            'https://www.xactanalysis.com/xactanalysis/search.jsp?date_type=received&date_preset=365&xasp_status_type=in_progress&columns=cache'
        ]
        
        for url in test_urls:
            print(f"\n🌐 Testing URL: {url}")
            try:
                response = manual_session.session.get(url, timeout=30, allow_redirects=True)
                print(f"  📊 Status: {response.status_code}")
                print(f"  🔗 Final URL: {response.url}")
                
                # Parse the response
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check for search forms
                forms = soup.find_all('form')
                print(f"  📝 Found {len(forms)} forms")
                
                for i, form in enumerate(forms):
                    action = form.get('action', 'No action')
                    method = form.get('method', 'GET')
                    inputs = form.find_all('input')
                    print(f"    Form {i+1}: {method} {action} ({len(inputs)} inputs)")
                    
                    # Check for search-related inputs
                    for input_field in inputs:
                        name = input_field.get('name', '')
                        type_attr = input_field.get('type', '')
                        placeholder = input_field.get('placeholder', '')
                        
                        if any(keyword in name.lower() + placeholder.lower() 
                               for keyword in ['search', 'claim', 'number', 'find', 'query']):
                            print(f"      🔍 Search input: {name} ({type_attr}) - {placeholder}")
                
                # Check for search links
                search_links = soup.find_all('a', href=lambda href: href and 'search' in href.lower())
                print(f"  🔗 Found {len(search_links)} search links")
                for link in search_links[:3]:  # Show first 3
                    href = link.get('href', '')
                    text = link.get_text().strip()[:50]
                    print(f"    Link: {href} - {text}")
                
                # Check for JavaScript that might handle search
                scripts = soup.find_all('script')
                search_js = []
                for script in scripts:
                    if script.string and any(keyword in script.string.lower() 
                                           for keyword in ['search', 'claim', 'query']):
                        search_js.append(script.string[:100] + "...")
                
                if search_js:
                    print(f"  🔧 Found {len(search_js)} scripts with search functionality")
                    for js in search_js[:2]:  # Show first 2
                        print(f"    JS: {js}")
                
                # Save the page for inspection
                filename = f"debug_page_{url.split('/')[-1] or 'main'}.html"
                filename = filename.replace('?', '_').replace('&', '_').replace('=', '_')
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"  💾 Saved page to {filename}")
                
            except Exception as e:
                print(f"  💥 Error: {e}")
        
        manual_session.close()
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_search_page()
