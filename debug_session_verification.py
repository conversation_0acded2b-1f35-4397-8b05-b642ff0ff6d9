#!/usr/bin/env python3
"""
Debug script to test session verification with loaded cookies.
"""

import json
import requests
from bs4 import BeautifulSoup
from pathlib import Path
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.automation_bs4.manual_session import ManualSession
    from src.utils.config_manager import ConfigManager
    from src.utils.logger import get_logger
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from src.automation_bs4.manual_session import ManualSession
    from src.utils.config_manager import ConfigManager
    from src.utils.logger import get_logger

def debug_session_verification():
    """Debug the session verification process."""
    
    # Initialize components
    config = ConfigManager()
    logger = get_logger(__name__, config)
    
    print("=== Session Verification Debug ===")
    
    # Check if cookies file exists
    cookie_file = Path('session_cookies.json')
    if not cookie_file.exists():
        print("❌ No session_cookies.json file found")
        return
    
    print("✅ Found session_cookies.json file")
    
    # Load cookies manually
    with open(cookie_file, 'r') as f:
        cookies_data = json.load(f)
    
    print(f"📄 Loaded {len(cookies_data)} cookies from file")
    
    # Create session and load cookies
    session = requests.Session()
    cookies_loaded = 0
    
    for cookie in cookies_data:
        try:
            session.cookies.set(
                cookie['name'],
                cookie['value'],
                domain=cookie.get('domain'),
                path=cookie.get('path'),
                secure=cookie.get('secure', False)
            )
            cookies_loaded += 1
        except Exception as e:
            print(f"⚠️  Failed to load cookie {cookie.get('name', 'unknown')}: {e}")
    
    print(f"🍪 Successfully loaded {cookies_loaded} cookies into session")
    
    # Test the session
    xact_main_url = 'https://www.xactanalysis.com'
    
    try:
        print(f"\n🌐 Testing session by accessing: {xact_main_url}")
        response = session.get(xact_main_url, timeout=30)
        print(f"📊 Response status: {response.status_code}")
        print(f"🔗 Final URL: {response.url}")
        
        # Parse the response
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for login indicators
        login_indicators = []
        login_text = soup.find(text=lambda text: text and 'login' in text.lower())
        signin_text = soup.find(text=lambda text: text and 'sign in' in text.lower())
        is_identity_url = 'identity.verisk.com' in response.url
        
        if login_text:
            login_indicators.append(f"Found 'login' text: {login_text.strip()[:50]}...")
        if signin_text:
            login_indicators.append(f"Found 'sign in' text: {signin_text.strip()[:50]}...")
        if is_identity_url:
            login_indicators.append(f"Redirected to identity URL: {response.url}")
        
        # Check for authenticated indicators
        auth_indicators = []
        dashboard_text = soup.find(text=lambda text: text and 'dashboard' in text.lower())
        logout_text = soup.find(text=lambda text: text and 'logout' in text.lower())
        nav_element = soup.find('nav')
        all_links = soup.find_all('a')
        
        if dashboard_text:
            auth_indicators.append(f"Found 'dashboard' text: {dashboard_text.strip()[:50]}...")
        if logout_text:
            auth_indicators.append(f"Found 'logout' text: {logout_text.strip()[:50]}...")
        if nav_element:
            auth_indicators.append("Found navigation element")
        auth_indicators.append(f"Found {len(all_links)} links on page")
        
        print(f"\n🔍 Login indicators found: {len(login_indicators)}")
        for indicator in login_indicators:
            print(f"  ❌ {indicator}")
        
        print(f"\n✅ Auth indicators found: {len(auth_indicators)}")
        for indicator in auth_indicators:
            print(f"  ✅ {indicator}")
        
        # Save response for debugging
        with open('debug_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"\n💾 Saved response to debug_response.html")
        
        # Determine session validity
        if any(login_indicators):
            print(f"\n❌ Session appears to be INVALID - login indicators found")
            return False
        elif any(auth_indicators):
            print(f"\n✅ Session appears to be VALID - auth indicators found")
            return True
        else:
            print(f"\n❓ Session status unclear - no clear indicators")
            return None
            
    except Exception as e:
        print(f"\n💥 Error testing session: {e}")
        return False

def test_manual_session():
    """Test using the ManualSession class."""
    print("\n=== Testing ManualSession Class ===")
    
    try:
        config = ConfigManager()
        manual_session = ManualSession(config)
        
        # Load saved cookies
        result = manual_session.load_saved_cookies()
        print(f"📋 Load cookies result: {result}")
        
        if result['success']:
            print("✅ ManualSession reports cookies loaded successfully")
        else:
            print(f"❌ ManualSession failed to load cookies: {result['message']}")
        
        manual_session.close()
        
    except Exception as e:
        print(f"💥 Error with ManualSession: {e}")

if __name__ == "__main__":
    # Run both tests
    debug_session_verification()
    test_manual_session()
