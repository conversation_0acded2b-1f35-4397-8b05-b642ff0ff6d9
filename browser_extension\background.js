// Background script for XactAnalysis automation extension

class ExtensionBackground {
    constructor() {
        this.webAppUrl = 'http://127.0.0.1:5000';
        this.connectedTabs = new Set();
        
        this.init();
    }
    
    init() {
        console.log('XactAnalysis Extension Background Script Loaded');
        
        // Listen for extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            console.log('Extension installed:', details);
            this.showWelcomeNotification();
        });
        
        // Listen for tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url && tab.url.includes('xactanalysis')) {
                this.onXactAnalysisPageLoaded(tabId, tab);
            }
        });
        
        // Listen for messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open
        });
    }
    
    showWelcomeNotification() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icon48.png',
            title: 'XactAnalysis Automation',
            message: 'Extension installed! Start the web app and click the extension icon to begin.'
        });
    }
    
    onXactAnalysisPageLoaded(tabId, tab) {
        console.log('XactAnalysis page loaded:', tab.url);
        this.connectedTabs.add(tabId);
        
        // Notify web app that XactAnalysis page is available
        this.notifyWebApp('xa_page_loaded', {
            tabId: tabId,
            url: tab.url,
            title: tab.title
        });
    }
    
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'checkWebApp':
                    const isConnected = await this.checkWebAppConnection();
                    sendResponse({ success: true, connected: isConnected });
                    break;
                    
                case 'notifyWebApp':
                    await this.notifyWebApp(request.event, request.data);
                    sendResponse({ success: true });
                    break;
                    
                case 'getNextClaim':
                    const nextClaim = await this.getNextClaimFromWebApp();
                    sendResponse(nextClaim);
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async checkWebAppConnection() {
        try {
            const response = await fetch(`${this.webAppUrl}/api/status`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }
    
    async notifyWebApp(event, data = {}) {
        try {
            await fetch(`${this.webAppUrl}/api/extension_event`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: event,
                    data: data,
                    timestamp: new Date().toISOString()
                })
            });
        } catch (error) {
            console.error('Failed to notify web app:', error);
        }
    }
    
    async getNextClaimFromWebApp() {
        try {
            const response = await fetch(`${this.webAppUrl}/api/get_next_claim`);
            const data = await response.json();
            return data;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

// Initialize background script
new ExtensionBackground();
