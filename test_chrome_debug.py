#!/usr/bin/env python3
"""
Test script to check if Chrome debug port is working.
"""

import requests
import json
import sys
from pathlib import Path

def test_debug_port(port=9222):
    """Test if Chrome debug port is responding."""
    try:
        print(f"🔍 Testing Chrome debug port {port}...")
        
        # Test version endpoint
        version_url = f"http://localhost:{port}/json/version"
        response = requests.get(version_url, timeout=5)
        
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Chrome debug port {port} is working!")
            print(f"   Browser: {version_info.get('Browser', 'Unknown')}")
            print(f"   Protocol Version: {version_info.get('Protocol-Version', 'Unknown')}")
            print(f"   User Agent: {version_info.get('User-Agent', 'Unknown')}")
            
            # Test tabs endpoint
            tabs_url = f"http://localhost:{port}/json"
            tabs_response = requests.get(tabs_url, timeout=5)
            
            if tabs_response.status_code == 200:
                tabs = tabs_response.json()
                print(f"   Open tabs: {len(tabs)}")
                
                # Show XactAnalysis tabs
                xa_tabs = [tab for tab in tabs if 'xactanalysis' in tab.get('url', '').lower()]
                if xa_tabs:
                    print(f"   XactAnalysis tabs found: {len(xa_tabs)}")
                    for i, tab in enumerate(xa_tabs[:3]):  # Show first 3
                        print(f"     {i+1}. {tab.get('title', 'No title')}")
                        print(f"        URL: {tab.get('url', 'No URL')}")
                else:
                    print("   ⚠️  No XactAnalysis tabs found")
                    print("   💡 Navigate to https://www.xactanalysis.com in Chrome")
            
            return True
            
        else:
            print(f"❌ Debug port {port} returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to debug port {port}")
        print(f"   Chrome is not running with --remote-debugging-port={port}")
        return False
        
    except requests.exceptions.Timeout:
        print(f"⏱️  Timeout connecting to debug port {port}")
        return False
        
    except Exception as e:
        print(f"❌ Error testing debug port {port}: {e}")
        return False

def main():
    """Test Chrome debug ports."""
    print("Chrome Debug Port Tester")
    print("=" * 40)
    print()
    
    # Test common ports
    ports_to_test = [9222, 9223, 9224, 9225]
    working_ports = []
    
    for port in ports_to_test:
        if test_debug_port(port):
            working_ports.append(port)
        print()
    
    print("=" * 40)
    
    if working_ports:
        print(f"✅ Working debug ports: {', '.join(map(str, working_ports))}")
        print()
        print("🎉 You can now connect to the automation!")
        print("   1. Go to the web interface")
        print(f"   2. Enter port: {working_ports[0]}")
        print("   3. Click 'Connect to Browser'")
        
    else:
        print("❌ No working debug ports found!")
        print()
        print("🔧 To fix this:")
        print("   1. Close all Chrome windows")
        print("   2. Run one of these commands:")
        print()
        print("   Windows Command Prompt:")
        print('   chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\\chrome_debug"')
        print()
        print("   Or use the provided launcher scripts:")
        print("   - launch_chrome_debug.bat")
        print("   - launch_chrome_debug.ps1")
        print()
        print("   3. Wait 10-15 seconds for Chrome to start")
        print("   4. Run this test script again")
    
    print()
    return 0 if working_ports else 1

if __name__ == "__main__":
    sys.exit(main())
