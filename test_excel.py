#!/usr/bin/env python3
"""
Quick test script for Excel processor functionality.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_manager import ConfigManager
from src.data.excel_processor import ExcelProcessor

def main():
    """Test the Excel processor with the actual data file."""
    try:
        print("Testing Excel Processor...")
        
        # Initialize
        config = ConfigManager()
        processor = ExcelProcessor(config)
        
        # Validate file structure
        print("\n1. Validating file structure...")
        processor.validate_file_structure()
        print("✓ File structure is valid")
        
        # Get file info
        print("\n2. Getting file information...")
        file_info = processor.get_file_info()
        for key, value in file_info.items():
            print(f"   {key}: {value}")
        
        # Load all claims
        print("\n3. Loading all claims...")
        all_claims = processor.load_claims()
        print(f"✓ Loaded {len(all_claims)} total claims")
        
        # Show first few claims
        print("\n4. Sample claims:")
        for i, claim in enumerate(all_claims[:5]):
            status = "✓ Completed" if claim['is_completed'] else "○ Pending"
            print(f"   {i+1}. {claim['claim_number']} (Row {claim['excel_row']}) - {status}")
        
        if len(all_claims) > 5:
            print(f"   ... and {len(all_claims) - 5} more")
        
        # Get pending claims
        print("\n5. Getting pending claims...")
        pending_claims = processor.get_pending_claims()
        print(f"✓ Found {len(pending_claims)} pending claims")
        
        # Summary
        completed_count = len(all_claims) - len(pending_claims)
        print(f"\n📊 Summary:")
        print(f"   Total claims: {len(all_claims)}")
        print(f"   Completed: {completed_count}")
        print(f"   Pending: {len(pending_claims)}")
        print(f"   Progress: {completed_count/len(all_claims)*100:.1f}%")
        
        print("\n✅ Excel processor test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
