"""
Simplified CSV processing module for XactAnalysis automation.

This module handles reading claim numbers from CSV files and tracking
completion status with 'D' markers.
"""

import csv
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import ConfigurationError


class CSVProcessor:
    """Handles CSV file processing for claim automation."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the CSV processor.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.claim_column = self.config.get_int('excel', 'claim_column', 1)  # Column B = 1
        self.status_column = self.config.get_int('excel', 'status_column', 0)  # Column A = 0
        self.completion_marker = self.config.get('excel', 'completion_marker', 'D')
        
        # Current file
        self.current_file = None
        self.df = None
        
        self.logger.info("CSV processor initialized")
    
    def load_csv(self, file_path: str) -> bool:
        """
        Load a CSV file for processing.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            True if file loaded successfully
            
        Raises:
            ConfigurationError: If file cannot be loaded
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise ConfigurationError(f"CSV file not found: {file_path}")
            
            self.logger.info(f"Loading CSV file: {file_path}")
            
            # Try to read the CSV file
            try:
                # First try with pandas (handles various formats better)
                self.df = pd.read_csv(file_path, dtype=str, keep_default_na=False)
            except Exception as e:
                self.logger.warning(f"Pandas read failed, trying with csv module: {e}")
                
                # Fallback to csv module
                with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                    # Try to detect delimiter
                    sample = csvfile.read(1024)
                    csvfile.seek(0)
                    sniffer = csv.Sniffer()
                    delimiter = sniffer.sniff(sample).delimiter
                    
                    reader = csv.reader(csvfile, delimiter=delimiter)
                    rows = list(reader)
                
                # Convert to DataFrame
                if rows:
                    # Use first row as headers if it looks like headers
                    if self._looks_like_header(rows[0]):
                        headers = rows[0]
                        data = rows[1:]
                    else:
                        # Generate column names
                        headers = [f'Column_{i}' for i in range(len(rows[0]))]
                        data = rows
                    
                    self.df = pd.DataFrame(data, columns=headers)
                else:
                    raise ConfigurationError("CSV file is empty")
            
            # Ensure we have enough columns
            if len(self.df.columns) <= max(self.claim_column, self.status_column):
                raise ConfigurationError(
                    f"CSV file doesn't have enough columns. "
                    f"Need at least {max(self.claim_column, self.status_column) + 1} columns."
                )
            
            self.current_file = file_path
            
            # Log file info
            total_claims = len(self.df)
            completed_claims = len(self.get_completed_claims())
            pending_claims = len(self.get_pending_claims())
            
            self.logger.info(f"CSV loaded successfully: {total_claims} total, {completed_claims} completed, {pending_claims} pending")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load CSV file: {e}")
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"Failed to load CSV file: {e}")
    
    def _looks_like_header(self, row: List[str]) -> bool:
        """
        Check if a row looks like a header row.
        
        Args:
            row: List of cell values
            
        Returns:
            True if row appears to be headers
        """
        # Check if row contains common header keywords
        header_keywords = ['claim', 'number', 'status', 'id', 'name', 'date']
        row_text = ' '.join(str(cell).lower() for cell in row)
        
        return any(keyword in row_text for keyword in header_keywords)
    
    def get_all_claims(self) -> List[Dict[str, Any]]:
        """
        Get all claims from the CSV file.
        
        Returns:
            List of claim dictionaries
        """
        if self.df is None:
            return []
        
        claims = []
        for index, row in self.df.iterrows():
            claim_number = str(row.iloc[self.claim_column]).strip()
            status = str(row.iloc[self.status_column]).strip()
            
            if claim_number and claim_number.lower() not in ['nan', 'none', '']:
                claims.append({
                    'index': index,
                    'claim_number': claim_number,
                    'status': status,
                    'is_completed': status == self.completion_marker,
                    'row_data': row.to_dict()
                })
        
        return claims
    
    def get_pending_claims(self) -> List[Dict[str, Any]]:
        """
        Get all pending (not completed) claims.
        
        Returns:
            List of pending claim dictionaries
        """
        all_claims = self.get_all_claims()
        return [claim for claim in all_claims if not claim['is_completed']]
    
    def get_completed_claims(self) -> List[Dict[str, Any]]:
        """
        Get all completed claims.
        
        Returns:
            List of completed claim dictionaries
        """
        all_claims = self.get_all_claims()
        return [claim for claim in all_claims if claim['is_completed']]
    
    def mark_claim_completed(self, claim_number: str) -> bool:
        """
        Mark a claim as completed by adding the completion marker.
        
        Args:
            claim_number: Claim number to mark as completed
            
        Returns:
            True if claim was marked successfully
        """
        if self.df is None:
            self.logger.error("No CSV file loaded")
            return False
        
        try:
            # Find the claim in the dataframe
            claim_mask = self.df.iloc[:, self.claim_column].astype(str).str.strip() == claim_number
            matching_rows = self.df[claim_mask]
            
            if matching_rows.empty:
                self.logger.warning(f"Claim {claim_number} not found in CSV")
                return False
            
            # Mark all matching rows as completed
            self.df.loc[claim_mask, self.df.columns[self.status_column]] = self.completion_marker
            
            self.logger.info(f"Marked claim {claim_number} as completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to mark claim {claim_number} as completed: {e}")
            return False
    
    def save_csv(self, file_path: Optional[str] = None) -> bool:
        """
        Save the current CSV file with updates.
        
        Args:
            file_path: Optional path to save to (defaults to current file)
            
        Returns:
            True if saved successfully
        """
        if self.df is None:
            self.logger.error("No CSV data to save")
            return False
        
        try:
            save_path = Path(file_path) if file_path else self.current_file
            
            if not save_path:
                self.logger.error("No file path specified for saving")
                return False
            
            # Create backup of original file
            if save_path.exists():
                backup_path = save_path.with_suffix(f'.backup{save_path.suffix}')
                save_path.rename(backup_path)
                self.logger.debug(f"Created backup: {backup_path}")
            
            # Save the updated CSV
            self.df.to_csv(save_path, index=False)
            
            self.logger.info(f"CSV file saved: {save_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save CSV file: {e}")
            return False
    
    def get_file_summary(self) -> Dict[str, Any]:
        """
        Get summary information about the current CSV file.
        
        Returns:
            Dictionary with file summary
        """
        if self.df is None:
            return {
                'file_path': None,
                'total_claims': 0,
                'completed_claims': 0,
                'pending_claims': 0,
                'progress_percent': 0
            }
        
        all_claims = self.get_all_claims()
        completed_claims = self.get_completed_claims()
        pending_claims = self.get_pending_claims()
        
        total = len(all_claims)
        completed = len(completed_claims)
        pending = len(pending_claims)
        
        progress_percent = round((completed / total * 100) if total > 0 else 0, 1)
        
        return {
            'file_path': str(self.current_file) if self.current_file else None,
            'total_claims': total,
            'completed_claims': completed,
            'pending_claims': pending,
            'progress_percent': progress_percent
        }
    
    def get_next_pending_claim(self) -> Optional[Dict[str, Any]]:
        """
        Get the next pending claim to process.
        
        Returns:
            Next pending claim dictionary or None
        """
        pending_claims = self.get_pending_claims()
        return pending_claims[0] if pending_claims else None
    
    def validate_csv_format(self) -> Tuple[bool, List[str]]:
        """
        Validate that the CSV file has the expected format.
        
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        if self.df is None:
            return False, ["No CSV file loaded"]
        
        # Check if we have enough columns
        required_columns = max(self.claim_column, self.status_column) + 1
        if len(self.df.columns) < required_columns:
            issues.append(f"CSV needs at least {required_columns} columns, found {len(self.df.columns)}")
        
        # Check if claim column has data
        claim_col_data = self.df.iloc[:, self.claim_column].dropna()
        if claim_col_data.empty:
            issues.append(f"Claim column (column {self.claim_column + 1}) appears to be empty")
        
        # Check for duplicate claim numbers
        claim_numbers = self.df.iloc[:, self.claim_column].astype(str).str.strip()
        duplicates = claim_numbers[claim_numbers.duplicated()].unique()
        if len(duplicates) > 0:
            issues.append(f"Found duplicate claim numbers: {', '.join(duplicates)}")
        
        # Check data types
        non_string_claims = []
        for idx, claim in enumerate(claim_numbers):
            if claim and not isinstance(claim, str) and str(claim).lower() not in ['nan', 'none']:
                try:
                    float(claim)  # Check if it's a number that should be treated as string
                    non_string_claims.append(claim)
                except:
                    pass
        
        if non_string_claims:
            issues.append(f"Some claim numbers may need formatting: {', '.join(non_string_claims[:5])}")
        
        return len(issues) == 0, issues
