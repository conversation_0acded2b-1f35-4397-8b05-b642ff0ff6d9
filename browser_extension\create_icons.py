#!/usr/bin/env python3
"""
Create simple icons for the browser extension.
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """Create a simple icon with the XA logo."""
    # Create a new image with a blue background
    img = Image.new('RGBA', (size, size), (0, 123, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw a white circle
    margin = size // 8
    draw.ellipse([margin, margin, size - margin, size - margin], fill='white')
    
    # Try to use a font, fall back to default if not available
    try:
        font_size = size // 3
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # Draw "XA" text
    text = "XA"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill=(0, 123, 255, 255), font=font)
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    """Create all required icon sizes."""
    # Create icons directory if it doesn't exist
    os.makedirs('browser_extension', exist_ok=True)
    
    # Create different sizes
    sizes = [16, 48, 128]
    
    for size in sizes:
        filename = f"browser_extension/icon{size}.png"
        create_icon(size, filename)
    
    print("All icons created successfully!")

if __name__ == "__main__":
    main()
