"""
Main automation controller for XactAnalysis claim processing.

This module orchestrates the entire automated process: CSV reading, login,
claim processing, PDF generation, and status updates.
"""

import time
from typing import Optional, Dict, Any, List
from pathlib import Path

from .xact_session import XactSession
from .claim_navigator import <PERSON><PERSON>m<PERSON><PERSON>gator
from .pdf_extractor import PDFExtractor
from .csv_processor import CSVProcessor
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import (
    LoginFailedException, ClaimNotFoundException, PDFGenerationError,
    NavigationError, ConfigurationError
)


class AutomationController:
    """Main controller for XactAnalysis automation."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the automation controller.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Initialize components
        self.session = XactSession(self.config)
        self.navigator = ClaimNavigator(self.session, self.config)
        self.pdf_extractor = PDFExtractor(self.session, self.config)
        self.csv_processor = CSVProcessor(self.config)
        
        # Configuration
        self.max_retries = self.config.get_int('retry', 'max_retries', 3)
        self.retry_delay = self.config.get_float('retry', 'retry_delay', 5.0)
        self.backoff_multiplier = self.config.get_float('retry', 'backoff_multiplier', 2.0)
        
        # State
        self.is_logged_in = False
        self.current_csv_file = None
        
        self.logger.info("Automation controller initialized")
    
    def load_csv_file(self, file_path: str) -> Dict[str, Any]:
        """
        Load a CSV file for processing.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Dictionary with load results and file summary
        """
        try:
            self.logger.info(f"Loading CSV file: {file_path}")
            
            # Load the CSV file
            success = self.csv_processor.load_csv(file_path)
            
            if success:
                self.current_csv_file = file_path
                
                # Validate CSV format
                is_valid, issues = self.csv_processor.validate_csv_format()
                
                # Get file summary
                summary = self.csv_processor.get_file_summary()
                
                result = {
                    'success': True,
                    'message': 'CSV file loaded successfully',
                    'file_path': file_path,
                    'is_valid': is_valid,
                    'validation_issues': issues,
                    'summary': summary
                }
                
                if not is_valid:
                    result['message'] += f" (with {len(issues)} validation issues)"
                
                return result
            else:
                return {
                    'success': False,
                    'message': 'Failed to load CSV file',
                    'file_path': file_path
                }
                
        except Exception as e:
            self.logger.error(f"Failed to load CSV file: {e}")
            return {
                'success': False,
                'message': f'Failed to load CSV file: {str(e)}',
                'file_path': file_path
            }
    
    def login(self) -> Dict[str, Any]:
        """
        Login to XactAnalysis.
        
        Returns:
            Dictionary with login results
        """
        try:
            self.logger.info("Attempting to login to XactAnalysis")
            
            success = self.session.login()
            
            if success:
                self.is_logged_in = True
                return {
                    'success': True,
                    'message': 'Successfully logged into XactAnalysis'
                }
            else:
                return {
                    'success': False,
                    'message': 'Login failed'
                }
                
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return {
                'success': False,
                'message': f'Login failed: {str(e)}'
            }
    
    def process_single_claim(self, claim_number: str) -> Dict[str, Any]:
        """
        Process a single claim.
        
        Args:
            claim_number: Claim number to process
            
        Returns:
            Dictionary with processing results
        """
        if not self.is_logged_in:
            return {
                'success': False,
                'message': 'Not logged in. Please login first.',
                'claim_number': claim_number
            }
        
        try:
            self.logger.info(f"Processing claim: {claim_number}")
            
            # Search for the claim
            claims = self.navigator.search_claim(claim_number)
            
            if not claims:
                return {
                    'success': False,
                    'message': f'Claim {claim_number} not found',
                    'claim_number': claim_number
                }
            
            # Process each claim result (handle multiple entries)
            processed_pdfs = []
            
            for i, claim_data in enumerate(claims):
                try:
                    # Navigate to notes
                    notes_url = self.navigator.navigate_to_notes(claim_data)
                    
                    # Extract PDF
                    pdf_path = self.pdf_extractor.extract_notes_pdf(claim_number, notes_url, i)
                    
                    processed_pdfs.append({
                        'pdf_path': pdf_path,
                        'suffix_index': i,
                        'notes_url': notes_url
                    })
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process claim variant {i} for {claim_number}: {e}")
                    continue
            
            if not processed_pdfs:
                return {
                    'success': False,
                    'message': f'Failed to extract any PDFs for claim {claim_number}',
                    'claim_number': claim_number
                }
            
            # Mark claim as completed in CSV if loaded
            if self.current_csv_file:
                self.csv_processor.mark_claim_completed(claim_number)
                self.csv_processor.save_csv()
            
            return {
                'success': True,
                'message': f'Successfully processed claim {claim_number}',
                'claim_number': claim_number,
                'pdfs_generated': len(processed_pdfs),
                'pdf_paths': [pdf['pdf_path'] for pdf in processed_pdfs]
            }
            
        except ClaimNotFoundException as e:
            return {
                'success': False,
                'message': f'Claim {claim_number} not found: {str(e)}',
                'claim_number': claim_number
            }
        except Exception as e:
            self.logger.error(f"Failed to process claim {claim_number}: {e}")
            return {
                'success': False,
                'message': f'Failed to process claim {claim_number}: {str(e)}',
                'claim_number': claim_number
            }
    
    def process_next_claim(self) -> Dict[str, Any]:
        """
        Process the next pending claim from the CSV file.
        
        Returns:
            Dictionary with processing results
        """
        if not self.current_csv_file:
            return {
                'success': False,
                'message': 'No CSV file loaded'
            }
        
        # Get next pending claim
        next_claim = self.csv_processor.get_next_pending_claim()
        
        if not next_claim:
            return {
                'success': True,
                'message': 'No pending claims to process',
                'completed': True
            }
        
        # Process the claim
        result = self.process_single_claim(next_claim['claim_number'])
        
        # Add claim info to result
        result['claim_info'] = next_claim
        
        return result
    
    def process_all_claims(self, max_claims: Optional[int] = None) -> Dict[str, Any]:
        """
        Process all pending claims from the CSV file.
        
        Args:
            max_claims: Maximum number of claims to process (None for all)
            
        Returns:
            Dictionary with processing results
        """
        if not self.current_csv_file:
            return {
                'success': False,
                'message': 'No CSV file loaded'
            }
        
        if not self.is_logged_in:
            return {
                'success': False,
                'message': 'Not logged in. Please login first.'
            }
        
        pending_claims = self.csv_processor.get_pending_claims()
        
        if not pending_claims:
            return {
                'success': True,
                'message': 'No pending claims to process',
                'completed': True,
                'processed_count': 0,
                'failed_count': 0
            }
        
        # Limit claims if specified
        if max_claims:
            pending_claims = pending_claims[:max_claims]
        
        self.logger.info(f"Starting batch processing of {len(pending_claims)} claims")
        
        processed_count = 0
        failed_count = 0
        failed_claims = []
        
        for claim in pending_claims:
            claim_number = claim['claim_number']
            
            try:
                result = self.process_single_claim(claim_number)
                
                if result['success']:
                    processed_count += 1
                    self.logger.info(f"Successfully processed claim {claim_number} ({processed_count}/{len(pending_claims)})")
                else:
                    failed_count += 1
                    failed_claims.append({
                        'claim_number': claim_number,
                        'error': result['message']
                    })
                    self.logger.warning(f"Failed to process claim {claim_number}: {result['message']}")
                
                # Add delay between claims to avoid overwhelming the server
                time.sleep(1)
                
            except Exception as e:
                failed_count += 1
                failed_claims.append({
                    'claim_number': claim_number,
                    'error': str(e)
                })
                self.logger.error(f"Unexpected error processing claim {claim_number}: {e}")
                continue
        
        return {
            'success': True,
            'message': f'Batch processing completed: {processed_count} successful, {failed_count} failed',
            'processed_count': processed_count,
            'failed_count': failed_count,
            'failed_claims': failed_claims,
            'total_claims': len(pending_claims)
        }
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the automation system.
        
        Returns:
            Dictionary with current status
        """
        status = {
            'is_logged_in': self.is_logged_in,
            'current_csv_file': self.current_csv_file,
            'csv_summary': None
        }
        
        if self.current_csv_file:
            status['csv_summary'] = self.csv_processor.get_file_summary()
        
        return status
    
    def cleanup(self):
        """Clean up resources and close connections."""
        try:
            if self.session:
                self.session.close()
            self.is_logged_in = False
            self.logger.info("Automation controller cleaned up")
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")
