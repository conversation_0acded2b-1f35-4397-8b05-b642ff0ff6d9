"""
Web application for XactAnalysis automation.

This module provides a web-based interface for controlling the XactAnalysis
automation system, allowing users to process claims through a simple UI.
"""

import os
import shutil
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
from werkzeug.utils import secure_filename

from flask import Flask, render_template, request, jsonify, send_from_directory, redirect, url_for, flash
from flask_cors import CORS

from .utils.config_manager import ConfigManager
from .utils.logger import get_logger
from .data.excel_processor import ExcelProcessor
from .automation.xact_analysis_bot import XactAnalysisBot
from .automation.pdf_generator import PDFGenerator


# Initialize Flask app
# Get the project root directory (parent of src)
project_root = Path(__file__).parent.parent
app = Flask(__name__,
           template_folder=str(project_root / 'templates'),
           static_folder=str(project_root / 'static'))
app.config['SECRET_KEY'] = 'xa-extraction-secret-key-2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
CORS(app)

# Global instances
config = ConfigManager()
logger = get_logger(__name__, config)
excel_processor = ExcelProcessor(config)
xa_bot = None
pdf_generator = PDFGenerator(config)

# Ensure directories exist
config.ensure_directories()


@app.route('/')
def index():
    """Main dashboard page."""
    try:
        # Get claims summary
        claims_summary = get_claims_summary()
        
        # Get system status
        system_status = get_system_status()
        
        return render_template('index.html', 
                             claims_summary=claims_summary,
                             system_status=system_status)
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        return render_template('error.html', error=str(e))


@app.route('/api/status')
def api_status():
    """Get current system status."""
    try:
        status = get_system_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/claims')
def api_claims():
    """Get claims data."""
    try:
        claims_data = get_claims_data()
        return jsonify(claims_data)
    except Exception as e:
        logger.error(f"Error getting claims: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/connect', methods=['POST'])
def api_connect():
    """Connect to existing browser session."""
    global xa_bot
    
    try:
        data = request.get_json() or {}
        debug_port = data.get('debug_port', 9222)
        
        logger.info(f"Attempting to connect to browser on port {debug_port}")
        
        # Clean up existing connection
        if xa_bot:
            xa_bot.cleanup()
        
        # Create new bot and connect
        xa_bot = XactAnalysisBot(config)
        success = xa_bot.connect_to_session(debug_port)
        
        if success:
            status = xa_bot.get_current_status()
            logger.info("Successfully connected to browser session")
            return jsonify({
                'success': True,
                'message': 'Connected to browser session',
                'status': status
            })
        else:
            logger.error("Failed to connect to browser session")
            return jsonify({
                'success': False,
                'message': 'Failed to connect to browser session'
            }), 400
            
    except Exception as e:
        logger.error(f"Error connecting to browser: {e}")
        return jsonify({
            'success': False,
            'message': f'Connection error: {str(e)}'
        }), 500


@app.route('/api/process_claim', methods=['POST'])
def api_process_claim():
    """Process a single claim."""
    try:
        data = request.get_json()
        claim_number = data.get('claim_number')

        if not claim_number:
            return jsonify({'success': False, 'message': 'Claim number required'}), 400

        if not xa_bot:
            return jsonify({'success': False, 'message': 'Browser not initialized'}), 400

        if not xa_bot.browser_manager.reconnect_if_needed():
            return jsonify({'success': False, 'message': 'Browser not connected and reconnection failed'}), 400

        logger.info(f"Processing claim: {claim_number}")

        # Process the claim
        result = process_single_claim(claim_number)

        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing claim: {e}")
        return jsonify({
            'success': False,
            'message': f'Processing error: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@app.route('/api/process_next', methods=['POST'])
def api_process_next():
    """Process the next pending claim."""
    try:
        # Check if browser is connected and try to reconnect if needed
        if not xa_bot:
            return jsonify({'success': False, 'message': 'Browser not initialized'}), 400

        if not xa_bot.browser_manager.reconnect_if_needed():
            return jsonify({'success': False, 'message': 'Browser not connected and reconnection failed'}), 400

        # Get next pending claim
        pending_claims = excel_processor.get_pending_claims()

        if not pending_claims:
            return jsonify({
                'success': True,
                'message': 'No pending claims to process',
                'completed': True
            })

        next_claim = pending_claims[0]
        claim_number = next_claim['claim_number']

        logger.info(f"Processing next claim: {claim_number}")

        # Process the claim
        result = process_single_claim(claim_number, next_claim['row_index'])

        # Add progress information
        result['progress'] = {
            'current': len(pending_claims) - (1 if result['success'] else 0),
            'total': len(excel_processor.load_claims()),
            'remaining': len(pending_claims) - (1 if result['success'] else 0)
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error processing next claim: {e}")
        return jsonify({
            'success': False,
            'message': f'Processing error: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@app.route('/api/upload_csv', methods=['POST'])
def api_upload_csv():
    """Upload and switch to a new CSV file."""
    try:
        # Check if file was uploaded
        if 'csv_file' not in request.files:
            return jsonify({'success': False, 'message': 'No file uploaded'}), 400

        file = request.files['csv_file']

        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400

        # Validate file extension
        if not file.filename.lower().endswith('.csv'):
            return jsonify({'success': False, 'message': 'File must be a CSV file'}), 400

        # Secure the filename
        filename = secure_filename(file.filename)

        # Create upload directory if it doesn't exist
        upload_dir = Path('User Input')
        upload_dir.mkdir(exist_ok=True)

        # Save the file
        file_path = upload_dir / filename
        file.save(str(file_path))

        logger.info(f"CSV file uploaded: {file_path}")

        # Validate the CSV structure
        try:
            import pandas as pd
            df = pd.read_csv(file_path)

            # Check if it has the expected columns
            if df.shape[1] < 2:
                os.unlink(file_path)  # Remove the uploaded file
                return jsonify({
                    'success': False,
                    'message': 'CSV must have at least 2 columns (status and claim number)'
                }), 400

            # Check for claim numbers in column 1
            claim_column_data = df.iloc[:, 1].dropna()
            if len(claim_column_data) == 0:
                os.unlink(file_path)  # Remove the uploaded file
                return jsonify({
                    'success': False,
                    'message': 'No claim numbers found in column 2'
                }), 400

            logger.info(f"CSV validation passed: {len(df)} rows, {df.shape[1]} columns")

        except Exception as e:
            if file_path.exists():
                os.unlink(file_path)  # Remove the uploaded file
            return jsonify({
                'success': False,
                'message': f'Invalid CSV format: {str(e)}'
            }), 400

        # Update configuration to use the new file
        global excel_processor

        # Backup current config
        old_file_path = config.excel_file_path

        # Update config file
        config_file_path = Path('config/config.ini')
        config_content = config_file_path.read_text()

        # Replace the excel_file line
        lines = config_content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('excel_file ='):
                lines[i] = f'excel_file = {file_path}'
                break

        # Write updated config
        config_file_path.write_text('\n'.join(lines))

        # Reload configuration and excel processor
        config.__init__()  # Reload config
        excel_processor = ExcelProcessor(config)

        # Get new file info
        file_info = excel_processor.get_file_info()
        claims_summary = get_claims_summary()

        logger.info(f"Switched to new CSV file: {file_path}")

        return jsonify({
            'success': True,
            'message': f'Successfully uploaded and switched to {filename}',
            'file_info': file_info,
            'claims_summary': claims_summary,
            'old_file': str(old_file_path),
            'new_file': str(file_path)
        })

    except Exception as e:
        logger.error(f"Error uploading CSV: {e}")
        return jsonify({
            'success': False,
            'message': f'Upload error: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@app.route('/api/current_file_info')
def api_current_file_info():
    """Get information about the current CSV file."""
    try:
        file_info = excel_processor.get_file_info()
        return jsonify({
            'success': True,
            'file_info': file_info
        })
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/extension_connect', methods=['POST'])
def api_extension_connect():
    """Handle browser extension connection."""
    try:
        data = request.get_json() or {}
        tab_id = data.get('tabId')
        url = data.get('url', '')
        title = data.get('title', '')

        logger.info(f"Browser extension connected - Tab {tab_id}: {title}")

        # Store extension connection info
        extension_info = {
            'connected': True,
            'tabId': tab_id,
            'url': url,
            'title': title,
            'timestamp': datetime.now().isoformat()
        }

        return jsonify({
            'success': True,
            'message': 'Extension connected successfully',
            'info': extension_info
        })

    except Exception as e:
        logger.error(f"Error connecting extension: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/extension_event', methods=['POST'])
def api_extension_event():
    """Handle events from browser extension."""
    try:
        data = request.get_json() or {}
        event = data.get('event')
        event_data = data.get('data', {})
        timestamp = data.get('timestamp')

        logger.info(f"Extension event: {event} - {event_data}")

        # Handle different event types
        if event == 'claim_found':
            claim_number = event_data.get('claimNumber')
            logger.info(f"Extension found claim: {claim_number}")

        elif event == 'notes_opened':
            logger.info("Extension navigated to notes section")

        elif event == 'pdf_generated':
            claim_number = event_data.get('claimNumber')
            logger.info(f"Extension generated PDF for claim: {claim_number}")

            # Mark claim as completed in Excel
            if claim_number:
                try:
                    excel_processor.mark_claim_completed(claim_number)
                    logger.info(f"Marked claim {claim_number} as completed in Excel")
                except Exception as e:
                    logger.error(f"Failed to mark claim {claim_number} as completed: {e}")

        elif event == 'xa_page_loaded':
            logger.info("XactAnalysis page loaded in browser")

        return jsonify({
            'success': True,
            'message': f'Event {event} processed successfully'
        })

    except Exception as e:
        logger.error(f"Error processing extension event: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/get_next_claim')
def api_get_next_claim():
    """Get the next pending claim for the extension."""
    try:
        pending_claims = excel_processor.get_pending_claims()

        if not pending_claims:
            return jsonify({
                'success': True,
                'message': 'No pending claims',
                'claimNumber': None,
                'completed': True
            })

        next_claim = pending_claims[0]
        claim_number = next_claim['claim_number']

        logger.info(f"Providing next claim to extension: {claim_number}")

        return jsonify({
            'success': True,
            'claimNumber': claim_number,
            'rowIndex': next_claim['row_index'],
            'totalPending': len(pending_claims),
            'message': f'Next claim: {claim_number}'
        })

    except Exception as e:
        logger.error(f"Error getting next claim: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


def process_single_claim(claim_number: str, row_index: int = None) -> Dict[str, Any]:
    """
    Process a single claim through the automation workflow.
    
    Args:
        claim_number: The claim number to process
        row_index: Optional row index for faster Excel updates
        
    Returns:
        Dictionary with processing results
    """
    try:
        start_time = datetime.now()
        
        # Step 1: Search for claim
        logger.info(f"Step 1: Searching for claim {claim_number}")
        search_success = xa_bot.search_claim(claim_number)
        
        if not search_success:
            return {
                'success': False,
                'claim_number': claim_number,
                'message': f'Claim {claim_number} not found',
                'step': 'search'
            }
        
        # Step 2: Navigate to notes
        logger.info(f"Step 2: Navigating to notes for claim {claim_number}")
        notes_success = xa_bot.navigate_to_notes()
        
        if not notes_success:
            return {
                'success': False,
                'claim_number': claim_number,
                'message': f'Could not navigate to notes for claim {claim_number}',
                'step': 'navigation'
            }
        
        # Step 3: Generate PDF(s)
        logger.info(f"Step 3: Generating PDF for claim {claim_number}")
        page = xa_bot.browser_manager.page
        
        # Check for multiple entries
        num_entries = pdf_generator.check_for_multiple_entries(page)
        generated_pdfs = []
        
        for entry_index in range(num_entries):
            if entry_index > 0:
                # Select the specific entry
                pdf_generator.select_entry(page, entry_index)
            
            # Generate PDF
            pdf_path = pdf_generator.generate_pdf_from_page(page, claim_number, entry_index)
            
            if pdf_path:
                generated_pdfs.append(str(pdf_path))
                logger.info(f"Generated PDF: {pdf_path}")
        
        if not generated_pdfs:
            return {
                'success': False,
                'claim_number': claim_number,
                'message': f'Failed to generate PDF for claim {claim_number}',
                'step': 'pdf_generation'
            }
        
        # Step 4: Mark as completed in Excel
        logger.info(f"Step 4: Marking claim {claim_number} as completed")
        excel_success = excel_processor.mark_claim_completed(claim_number, row_index)
        
        if not excel_success:
            logger.warning(f"Failed to mark claim {claim_number} as completed in Excel")
        
        # Calculate processing time
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        return {
            'success': True,
            'claim_number': claim_number,
            'message': f'Successfully processed claim {claim_number}',
            'generated_pdfs': generated_pdfs,
            'num_entries': num_entries,
            'processing_time': processing_time,
            'excel_updated': excel_success
        }
        
    except Exception as e:
        logger.error(f"Error processing claim {claim_number}: {e}")
        return {
            'success': False,
            'claim_number': claim_number,
            'message': f'Processing failed: {str(e)}',
            'error': str(e),
            'traceback': traceback.format_exc()
        }


def get_system_status() -> Dict[str, Any]:
    """Get current system status."""
    try:
        browser_connected = xa_bot and xa_bot.browser_manager.is_connected()
        
        status = {
            'browser_connected': browser_connected,
            'current_url': xa_bot.browser_manager.get_current_url() if browser_connected else None,
            'page_title': xa_bot.browser_manager.get_page_title() if browser_connected else None,
            'output_dir': str(config.output_dir),
            'excel_file': str(config.excel_file_path),
            'timestamp': datetime.now().isoformat()
        }
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return {'error': str(e)}


def get_claims_summary() -> Dict[str, Any]:
    """Get claims summary data."""
    try:
        all_claims = excel_processor.load_claims()
        pending_claims = excel_processor.get_pending_claims()
        
        completed_count = len(all_claims) - len(pending_claims)
        
        return {
            'total': len(all_claims),
            'completed': completed_count,
            'pending': len(pending_claims),
            'progress_percent': round((completed_count / len(all_claims)) * 100, 1) if all_claims else 0
        }
        
    except Exception as e:
        logger.error(f"Error getting claims summary: {e}")
        return {'error': str(e)}


def get_claims_data() -> Dict[str, Any]:
    """Get detailed claims data."""
    try:
        all_claims = excel_processor.load_claims()
        pending_claims = excel_processor.get_pending_claims()
        
        # Get recent claims (last 10)
        recent_claims = all_claims[-10:] if len(all_claims) > 10 else all_claims
        
        return {
            'summary': get_claims_summary(),
            'recent_claims': recent_claims,
            'next_pending': pending_claims[:5] if pending_claims else []
        }
        
    except Exception as e:
        logger.error(f"Error getting claims data: {e}")
        return {'error': str(e)}


if __name__ == '__main__':
    logger.info("Starting XactAnalysis automation web application")
    app.run(debug=True, host='127.0.0.1', port=5000)
