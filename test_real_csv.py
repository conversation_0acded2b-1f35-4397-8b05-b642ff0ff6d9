#!/usr/bin/env python3
"""
Test script to try the real CSV file with the Beautiful Soup automation.

This script tests the system with the actual Adam Jobs CSV file.
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.automation_bs4.automation_controller import AutomationController
from src.utils.config_manager import ConfigManager


def main():
    """Test with the real CSV file."""
    print("🧪 Testing XactAnalysis Automation with Real CSV")
    print("=" * 60)
    
    try:
        # Initialize the automation controller
        print("🤖 Initializing automation controller...")
        controller = AutomationController()
        
        # Load the real CSV file
        csv_path = "User Input/Adam Jobs in 24.xlsx - Sheet2.csv"
        print(f"📄 Loading CSV file: {csv_path}")
        
        result = controller.load_csv_file(csv_path)
        
        if not result['success']:
            print(f"❌ Failed to load CSV: {result['message']}")
            return 1
        
        print("✅ CSV loaded successfully!")
        print(f"   📊 Summary: {result['summary']}")
        
        if result['validation_issues']:
            print("⚠️  Validation issues found:")
            for issue in result['validation_issues']:
                print(f"   • {issue}")
        
        # Test login
        print("\n🔐 Testing login...")
        login_result = controller.login()
        
        if login_result['success']:
            print("✅ Login successful!")
            
            # Get next pending claim
            print("\n🔍 Getting next pending claim...")
            next_claim = controller.csv_processor.get_next_pending_claim()
            
            if next_claim:
                claim_number = next_claim['claim_number']
                print(f"📋 Next claim to process: {claim_number}")
                
                # Test processing a single claim
                print(f"\n⚡ Testing claim processing for: {claim_number}")
                process_result = controller.process_single_claim(claim_number)
                
                if process_result['success']:
                    print("✅ Claim processed successfully!")
                    print(f"   📁 PDFs generated: {process_result.get('pdfs_generated', 0)}")
                    if 'pdf_paths' in process_result:
                        for pdf_path in process_result['pdf_paths']:
                            print(f"   📄 PDF saved: {pdf_path}")
                else:
                    print(f"❌ Claim processing failed: {process_result['message']}")
            else:
                print("ℹ️  No pending claims found")
                
        else:
            print(f"❌ Login failed: {login_result['message']}")
            print("💡 This might be expected if XactAnalysis is not accessible or credentials are invalid")
        
        # Show final status
        print("\n📊 Final Status:")
        status = controller.get_status()
        if status['csv_summary']:
            summary = status['csv_summary']
            print(f"   Total claims: {summary['total_claims']}")
            print(f"   Completed: {summary['completed_claims']}")
            print(f"   Pending: {summary['pending_claims']}")
            print(f"   Progress: {summary['progress_percent']}%")
        
        # Cleanup
        controller.cleanup()
        
        print("\n🎉 Test completed successfully!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
