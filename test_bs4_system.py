#!/usr/bin/env python3
"""
Test script for XactAnalysis Beautiful Soup Automation.

This script performs comprehensive testing of the automated system.
"""

import os
import sys
import csv
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.automation_bs4.automation_controller import AutomationController
from src.automation_bs4.csv_processor import CSVProcessor
from src.utils.config_manager import ConfigManager


def print_banner():
    """Print test banner."""
    print("=" * 70)
    print("🧪 XactAnalysis Automated Processor Test Suite")
    print("=" * 70)
    print()


def create_test_csv():
    """Create a test CSV file."""
    test_data = [
        ['Status', 'Claim Number', 'Description'],
        ['', '12345678', 'Test claim 1'],
        ['D', '87654321', 'Already completed claim'],
        ['', '11111111', 'Test claim 2'],
        ['', '22222222', 'Test claim 3'],
    ]
    
    # Create temporary CSV file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    
    writer = csv.writer(temp_file)
    writer.writerows(test_data)
    temp_file.close()
    
    return temp_file.name


def test_configuration():
    """Test configuration loading."""
    print("🔧 Testing Configuration...")
    
    try:
        config = ConfigManager()
        
        # Test basic configuration values
        base_url = config.get('xactanalysis', 'base_url', 'default')
        claim_column = config.get_int('csv', 'claim_column', 1)
        status_column = config.get_int('csv', 'status_column', 0)
        
        print(f"   ✅ Base URL: {base_url}")
        print(f"   ✅ Claim column: {claim_column}")
        print(f"   ✅ Status column: {status_column}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False


def test_csv_processor():
    """Test CSV processing functionality."""
    print("📊 Testing CSV Processor...")
    
    try:
        # Create test CSV
        test_csv_path = create_test_csv()
        
        # Test CSV processor
        processor = CSVProcessor()
        
        # Load CSV
        success = processor.load_csv(test_csv_path)
        if not success:
            print("   ❌ Failed to load test CSV")
            return False
        
        print("   ✅ CSV loaded successfully")
        
        # Test getting claims
        all_claims = processor.get_all_claims()
        pending_claims = processor.get_pending_claims()
        completed_claims = processor.get_completed_claims()
        
        print(f"   ✅ Total claims: {len(all_claims)}")
        print(f"   ✅ Pending claims: {len(pending_claims)}")
        print(f"   ✅ Completed claims: {len(completed_claims)}")
        
        # Test validation
        is_valid, issues = processor.validate_csv_format()
        print(f"   ✅ CSV validation: {'Valid' if is_valid else 'Issues found'}")
        if issues:
            for issue in issues:
                print(f"      ⚠️  {issue}")
        
        # Test marking completion
        test_claim = pending_claims[0]['claim_number'] if pending_claims else None
        if test_claim:
            success = processor.mark_claim_completed(test_claim)
            if success:
                print(f"   ✅ Successfully marked claim {test_claim} as completed")
            else:
                print(f"   ❌ Failed to mark claim {test_claim} as completed")
        
        # Cleanup
        os.unlink(test_csv_path)
        
        return True
        
    except Exception as e:
        print(f"   ❌ CSV processor test failed: {e}")
        return False


def test_session_creation():
    """Test session creation (without actual login)."""
    print("🔐 Testing Session Creation...")
    
    try:
        from src.automation_bs4.xact_session import XactSession
        
        # Create session
        session = XactSession()
        
        print("   ✅ Session object created")
        print(f"   ✅ Base URL: {session.base_url}")
        print(f"   ✅ Timeout: {session.timeout}")
        
        # Test credential checking (without actual credentials)
        try:
            username, password = session.get_credentials()
            print("   ✅ Credentials found in environment")
        except Exception as e:
            print(f"   ⚠️  Credentials not found: {e}")
        
        # Cleanup
        session.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Session creation test failed: {e}")
        return False


def test_automation_controller():
    """Test automation controller initialization."""
    print("🤖 Testing Automation Controller...")
    
    try:
        # Create controller
        controller = AutomationController()
        
        print("   ✅ Controller created successfully")
        
        # Test status
        status = controller.get_status()
        print(f"   ✅ Status retrieved: logged_in={status['is_logged_in']}")
        
        # Test CSV loading with test file
        test_csv_path = create_test_csv()
        
        result = controller.load_csv_file(test_csv_path)
        if result['success']:
            print("   ✅ CSV loading works")
            print(f"      Total claims: {result['summary']['total_claims']}")
            print(f"      Pending claims: {result['summary']['pending_claims']}")
        else:
            print(f"   ❌ CSV loading failed: {result['message']}")
        
        # Cleanup
        controller.cleanup()
        os.unlink(test_csv_path)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Automation controller test failed: {e}")
        return False


def test_web_app_imports():
    """Test web application imports."""
    print("🌐 Testing Web Application...")
    
    try:
        from src.automation_bs4.web_app import app
        
        print("   ✅ Web app imports successfully")
        print(f"   ✅ App name: {app.name}")
        
        # Test app configuration
        with app.app_context():
            print("   ✅ App context works")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Web app test failed: {e}")
        return False


def test_dependencies():
    """Test required dependencies."""
    print("📦 Testing Dependencies...")
    
    required_packages = [
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('pandas', 'pandas'),
        ('flask', 'flask'),
        ('lxml', 'lxml')
    ]
    
    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            print(f"   ❌ {package_name} - MISSING")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"   ⚠️  Missing packages: {', '.join(missing_packages)}")
        print("   💡 Run: pip install -r requirements.txt")
        return False
    
    return True


def test_directories():
    """Test required directories."""
    print("📁 Testing Directories...")
    
    required_dirs = ['output', 'logs', 'uploads']
    
    for directory in required_dirs:
        path = Path(directory)
        if path.exists():
            print(f"   ✅ {directory}")
        else:
            print(f"   ⚠️  {directory} - creating...")
            path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ {directory} - created")
    
    return True


def run_all_tests():
    """Run all tests."""
    print_banner()
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Directories", test_directories),
        ("Configuration", test_configuration),
        ("CSV Processor", test_csv_processor),
        ("Session Creation", test_session_creation),
        ("Automation Controller", test_automation_controller),
        ("Web Application", test_web_app_imports),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"   ❌ {test_name} test crashed: {e}")
            failed += 1
        
        print()
    
    # Summary
    print("=" * 70)
    print("📊 Test Summary")
    print("=" * 70)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    print()
    
    if failed == 0:
        print("🎉 All tests passed! The system is ready to use.")
        print()
        print("Next steps:")
        print("1. Set your XactAnalysis credentials in .env file")
        print("2. Run: python run_bs4_app.py")
        print("3. Upload a CSV file and start processing!")
    else:
        print("⚠️  Some tests failed. Please fix the issues before using the system.")
        print()
        print("Common fixes:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Run setup script: python setup_bs4.py")
        print("3. Check configuration files")
    
    return failed == 0


def main():
    """Main test function."""
    try:
        success = run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n👋 Tests cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
