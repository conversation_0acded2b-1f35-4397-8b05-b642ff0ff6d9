"""
PDF generation module for XactAnalysis notes.

This module handles the generation and saving of PDF files from XactAnalysis
notes pages, including proper naming conventions and multiple entry handling.
"""

import time
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import PDFGenerationError, FileOperationError


class PDFGenerator:
    """Handles PDF generation from XactAnalysis notes."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the PDF generator.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.output_dir = self.config.output_dir
        self.filename_template = self.config.get('pdf', 'filename_template', '{claim_number}{suffix}.pdf')
        self.multiple_suffixes = self.config.get_list('pdf', 'multiple_suffixes',
                                                     '_A,_B,_<PERSON>,_D,_E,_F,_G,_H,_I,_<PERSON>')
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("PDF generator initialized")
    
    def generate_pdf_from_page(self, page, claim_number: str, entry_index: int = 0) -> Optional[Path]:
        """
        Generate PDF from the current page.
        
        Args:
            page: Playwright page object
            claim_number: The claim number for naming
            entry_index: Index for multiple entries (0 for first/single entry)
            
        Returns:
            Path to generated PDF file if successful, None otherwise
            
        Raises:
            PDFGenerationError: If PDF generation fails
        """
        try:
            self.logger.info(f"Generating PDF for claim {claim_number}, entry {entry_index}")
            
            # Determine filename
            suffix = self.multiple_suffixes[entry_index] if entry_index > 0 else ''
            filename = self.filename_template.format(
                claim_number=claim_number,
                suffix=suffix
            )
            
            pdf_path = self.output_dir / filename
            
            # Check if file already exists
            if pdf_path.exists():
                self.logger.warning(f"PDF file already exists: {pdf_path}")
                # Create backup name
                counter = 1
                while pdf_path.exists():
                    name_parts = pdf_path.stem, counter, pdf_path.suffix
                    pdf_path = self.output_dir / f"{name_parts[0]}_backup_{name_parts[1]}{name_parts[2]}"
                    counter += 1
            
            # Generate PDF using browser's print function
            pdf_bytes = page.pdf(
                path=str(pdf_path),
                format='A4',
                print_background=True,
                margin={
                    'top': '0.5in',
                    'right': '0.5in',
                    'bottom': '0.5in',
                    'left': '0.5in'
                }
            )
            
            # Verify file was created and has content
            if not pdf_path.exists():
                raise PDFGenerationError(f"PDF file was not created: {pdf_path}")
            
            file_size = pdf_path.stat().st_size
            if file_size == 0:
                pdf_path.unlink()  # Remove empty file
                raise PDFGenerationError(f"Generated PDF is empty: {pdf_path}")
            
            self.logger.info(f"Successfully generated PDF: {pdf_path} ({file_size} bytes)")
            return pdf_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate PDF for claim {claim_number}: {e}")
            if isinstance(e, PDFGenerationError):
                raise
            raise PDFGenerationError(f"PDF generation failed: {e}")
    
    def generate_pdf_via_print_dialog(self, page, claim_number: str, entry_index: int = 0) -> Optional[Path]:
        """
        Generate PDF by triggering the browser's print dialog.
        
        Args:
            page: Playwright page object
            claim_number: The claim number for naming
            entry_index: Index for multiple entries
            
        Returns:
            Path to generated PDF file if successful, None otherwise
        """
        try:
            self.logger.info(f"Generating PDF via print dialog for claim {claim_number}")
            
            # Determine filename
            suffix = self.multiple_suffixes[entry_index] if entry_index > 0 else ''
            filename = self.filename_template.format(
                claim_number=claim_number,
                suffix=suffix
            )
            
            pdf_path = self.output_dir / filename
            
            # Take screenshot before print
            page.screenshot(path=str(self.output_dir / f"before_print_{claim_number}.png"))
            
            # Look for print button or trigger print
            print_selectors = [
                'button:has-text("Print")',
                'a:has-text("Print")',
                '[title="Print"]',
                '.print-button',
                '#print-btn',
                '[data-action="print"]'
            ]
            
            print_clicked = False
            for selector in print_selectors:
                try:
                    if page.query_selector(selector):
                        page.click(selector)
                        print_clicked = True
                        self.logger.debug(f"Clicked print with selector: {selector}")
                        break
                except:
                    continue
            
            if not print_clicked:
                # Try keyboard shortcut
                try:
                    page.keyboard.press('Control+P')
                    print_clicked = True
                    self.logger.debug("Triggered print with Ctrl+P")
                except:
                    pass
            
            if not print_clicked:
                raise PDFGenerationError("Could not trigger print dialog")
            
            # Wait for print dialog
            time.sleep(2)
            
            # This method requires manual intervention or additional setup
            # for handling the print dialog automatically
            self.logger.warning("Print dialog triggered - manual intervention may be required")
            
            return pdf_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate PDF via print dialog: {e}")
            raise PDFGenerationError(f"Print dialog PDF generation failed: {e}")
    
    def check_for_multiple_entries(self, page) -> int:
        """
        Check if there are multiple entries for the current claim.
        
        Args:
            page: Playwright page object
            
        Returns:
            Number of entries found
        """
        try:
            # Look for indicators of multiple entries
            multiple_indicators = [
                '.entry-list li',
                '.note-entry',
                '[data-entry]',
                'table tbody tr',
                '.claim-entry'
            ]
            
            max_entries = 1
            for selector in multiple_indicators:
                try:
                    elements = page.query_selector_all(selector)
                    if elements and len(elements) > max_entries:
                        max_entries = len(elements)
                        self.logger.debug(f"Found {len(elements)} entries with selector: {selector}")
                except:
                    continue
            
            self.logger.info(f"Detected {max_entries} entries for current claim")
            return max_entries
            
        except Exception as e:
            self.logger.warning(f"Error checking for multiple entries: {e}")
            return 1
    
    def select_entry(self, page, entry_index: int) -> bool:
        """
        Select a specific entry for PDF generation.
        
        Args:
            page: Playwright page object
            entry_index: Index of entry to select (0-based)
            
        Returns:
            True if entry selected successfully
        """
        try:
            if entry_index == 0:
                # First entry is usually already selected
                return True
            
            # Look for entry selection elements
            entry_selectors = [
                f'.entry-list li:nth-child({entry_index + 1})',
                f'.note-entry:nth-child({entry_index + 1})',
                f'[data-entry="{entry_index}"]',
                f'table tbody tr:nth-child({entry_index + 1})'
            ]
            
            for selector in entry_selectors:
                try:
                    element = page.query_selector(selector)
                    if element:
                        element.click()
                        time.sleep(1)  # Wait for selection
                        self.logger.debug(f"Selected entry {entry_index} with selector: {selector}")
                        return True
                except:
                    continue
            
            self.logger.warning(f"Could not select entry {entry_index}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error selecting entry {entry_index}: {e}")
            return False
    
    def get_existing_pdfs(self, claim_number: str) -> List[Path]:
        """
        Get list of existing PDF files for a claim.
        
        Args:
            claim_number: The claim number to check
            
        Returns:
            List of existing PDF file paths
        """
        try:
            existing_pdfs = []
            
            # Check for main file
            main_filename = self.filename_template.format(claim_number=claim_number, suffix='')
            main_path = self.output_dir / main_filename
            if main_path.exists():
                existing_pdfs.append(main_path)
            
            # Check for multiple entry files
            for suffix in self.multiple_suffixes:
                filename = self.filename_template.format(claim_number=claim_number, suffix=suffix)
                file_path = self.output_dir / filename
                if file_path.exists():
                    existing_pdfs.append(file_path)
            
            return existing_pdfs
            
        except Exception as e:
            self.logger.error(f"Error checking existing PDFs for claim {claim_number}: {e}")
            return []
    
    def cleanup_temp_files(self, claim_number: str) -> None:
        """
        Clean up temporary files for a claim.
        
        Args:
            claim_number: The claim number to clean up
        """
        try:
            # Clean up screenshots
            screenshot_patterns = [
                f"before_print_{claim_number}.png",
                f"after_print_{claim_number}.png",
                f"error_print_{claim_number}.png"
            ]
            
            for pattern in screenshot_patterns:
                file_path = self.output_dir / pattern
                if file_path.exists():
                    file_path.unlink()
                    self.logger.debug(f"Cleaned up temp file: {file_path}")
            
        except Exception as e:
            self.logger.warning(f"Error cleaning up temp files for claim {claim_number}: {e}")
