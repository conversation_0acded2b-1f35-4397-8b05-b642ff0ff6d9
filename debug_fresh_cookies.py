#!/usr/bin/env python3
"""
Debug script to test the fresh cookies directly.
"""

import json
import requests
from bs4 import BeautifulSoup
from pathlib import Path

def test_fresh_cookies():
    """Test the fresh cookies directly."""
    
    print("=== Testing Fresh Cookies ===")
    
    # Load the fresh cookies
    fresh_cookies_file = Path('fresh_cookies_formatted.json')
    if not fresh_cookies_file.exists():
        print("❌ Fresh cookies file not found")
        return
    
    with open(fresh_cookies_file, 'r') as f:
        cookies_data = json.load(f)
    
    print(f"📄 Loaded {len(cookies_data)} fresh cookies")
    
    # Create session and load cookies
    session = requests.Session()
    cookies_loaded = 0
    
    for cookie in cookies_data:
        try:
            # Handle domain variations
            domain = cookie.get('domain', '')
            if domain.startswith('.www.'):
                domain = domain[1:]  # Remove leading dot from .www.
            elif domain.startswith('.'):
                domain = domain  # Keep leading dot for .xactanalysis.com
            
            session.cookies.set(
                cookie['name'],
                cookie['value'],
                domain=domain,
                path=cookie.get('path', '/'),
                secure=cookie.get('secure', False)
            )
            cookies_loaded += 1
            print(f"  ✅ Loaded: {cookie['name']} for {domain}")
        except Exception as e:
            print(f"  ❌ Failed to load cookie {cookie.get('name', 'unknown')}: {e}")
    
    print(f"\n🍪 Successfully loaded {cookies_loaded} cookies into session")
    
    # Test the session with different URLs
    test_urls = [
        'https://www.xactanalysis.com',
        'https://www.xactanalysis.com/apps/routing/routeUser.do',
        'https://www.xactanalysis.com/apps/main/main.do'
    ]
    
    for url in test_urls:
        print(f"\n🌐 Testing URL: {url}")
        try:
            response = session.get(url, timeout=30, allow_redirects=True)
            print(f"  📊 Status: {response.status_code}")
            print(f"  🔗 Final URL: {response.url}")
            
            # Check if redirected to login
            if 'identity.verisk.com' in response.url:
                print(f"  ❌ Redirected to login page")
            else:
                print(f"  ✅ Stayed on XactAnalysis domain")
                
                # Parse content
                soup = BeautifulSoup(response.content, 'html.parser')
                title = soup.find('title')
                if title:
                    print(f"  📄 Page title: {title.get_text().strip()}")
                
                # Look for authentication indicators
                nav = soup.find('nav')
                links = soup.find_all('a')
                print(f"  📊 Found {len(links)} links, nav element: {'Yes' if nav else 'No'}")
                
                # Save response for inspection
                filename = f"debug_response_{url.split('/')[-1] or 'main'}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"  💾 Saved response to {filename}")
                
        except Exception as e:
            print(f"  💥 Error: {e}")
    
    # Check cookie jar after requests
    print(f"\n🍪 Cookies in session after requests:")
    for cookie in session.cookies:
        print(f"  {cookie.name}: {cookie.value[:30]}... (domain: {cookie.domain})")

if __name__ == "__main__":
    test_fresh_cookies()
