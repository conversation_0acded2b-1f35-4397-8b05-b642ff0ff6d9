#!/usr/bin/env python3
"""
Debug script to understand Excel update issues.
"""

import sys
from pathlib import Path
import pandas as pd
from openpyxl import load_workbook

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_manager import ConfigManager

def main():
    """Debug Excel file operations."""
    try:
        print("Debugging Excel Operations...")
        
        config = ConfigManager()
        excel_path = config.excel_file_path
        
        print(f"Excel file: {excel_path}")
        
        # Read with pandas
        print("\n1. Reading with pandas:")
        df = pd.read_excel(excel_path, sheet_name=0)
        print(f"Shape: {df.shape}")
        print("First few rows of status column (index 0):")
        print(df.iloc[:5, 0])
        
        # Read with openpyxl
        print("\n2. Reading with openpyxl:")
        workbook = load_workbook(excel_path)
        worksheet = workbook.active
        
        print("First few cells in column A:")
        for row in range(1, 6):
            cell_value = worksheet.cell(row=row, column=1).value
            print(f"  Row {row}: {cell_value}")
        
        # Test writing to a cell
        print("\n3. Testing write operation:")
        test_row = 2  # First data row
        test_cell = worksheet.cell(row=test_row, column=1)
        original_value = test_cell.value
        print(f"Original value in A{test_row}: {original_value}")
        
        # Write test value
        test_cell.value = "TEST_MARKER"
        workbook.save(excel_path)
        workbook.close()
        
        # Verify write
        workbook2 = load_workbook(excel_path)
        worksheet2 = workbook2.active
        new_value = worksheet2.cell(row=test_row, column=1).value
        print(f"New value in A{test_row}: {new_value}")
        
        # Restore original value
        worksheet2.cell(row=test_row, column=1).value = original_value
        workbook2.save(excel_path)
        workbook2.close()
        
        print(f"Restored original value: {original_value}")
        
        print("\n✅ Debug completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
