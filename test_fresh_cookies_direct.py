#!/usr/bin/env python3
"""
Test the fresh cookies with the improved session verification.
"""

import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_fresh_cookies_with_improved_verification():
    """Test fresh cookies with improved session verification."""
    
    try:
        from src.automation_bs4.manual_session import ManualSession
        from src.utils.config_manager import ConfigManager
        
        print("=== Testing Fresh Cookies with Improved Verification ===")
        
        # Load fresh cookies
        with open('fresh_cookies_formatted.json', 'r') as f:
            fresh_cookies = f.read()
        
        config = ConfigManager()
        manual_session = ManualSession(config)
        
        # Load fresh cookies
        print("📋 Loading fresh cookies...")
        result = manual_session.load_cookies_from_text(fresh_cookies)
        
        print(f"\n📊 Result: {json.dumps(result, indent=2)}")
        
        if result['success']:
            print("\n✅ Session verification PASSED!")
            print("🎉 Your fresh cookies are working!")
            if 'details' in result:
                print("📝 Success details:")
                for detail in result['details']:
                    print(f"  • {detail}")
        else:
            print("\n❌ Session verification FAILED!")
            print(f"💬 Message: {result['message']}")
            
            if 'details' in result:
                print("📝 Failure details:")
                for detail in result['details']:
                    print(f"  • {detail}")
            
            if 'final_url' in result:
                print(f"🔗 Final URL: {result['final_url']}")
            
            if 'suggestion' in result:
                print(f"💡 Suggestion: {result['suggestion']}")
        
        manual_session.close()
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fresh_cookies_with_improved_verification()
