#!/usr/bin/env python3
"""
Test the updated search functionality.
"""

import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_updated_search():
    """Test the updated search functionality."""
    
    try:
        from src.automation_bs4.manual_session import ManualSession
        from src.automation_bs4.claim_navigator import ClaimNavigator
        from src.utils.config_manager import ConfigManager
        
        print("=== Testing Updated Search Functionality ===")
        
        # Load fresh cookies
        with open('fresh_cookies_formatted.json', 'r') as f:
            fresh_cookies = f.read()
        
        config = ConfigManager()
        manual_session = ManualSession(config)
        
        # Load fresh cookies
        print("📋 Loading fresh cookies...")
        result = manual_session.load_cookies_from_text(fresh_cookies)
        
        if not result['success']:
            print(f"❌ Failed to load cookies: {result['message']}")
            return
        
        print("✅ Cookies loaded successfully")
        
        # Create claim navigator
        navigator = ClaimNavigator(manual_session, config)
        
        # Test with a sample claim number
        test_claim = "0758991673"  # From the logs
        
        print(f"\n🔍 Testing search for claim: {test_claim}")
        
        try:
            # Test the search endpoint finding
            search_url, search_params = navigator._find_search_endpoint(test_claim)
            print(f"✅ Found search endpoint: {search_url}")
            print(f"📋 Search parameters: {search_params}")
            
            # Test the actual search
            print(f"\n🌐 Performing search...")
            search_response = navigator._perform_search(search_url, search_params, test_claim)
            print(f"📊 Search response status: {search_response.status_code}")
            print(f"🔗 Search response URL: {search_response.url}")
            
            # Save search response for inspection
            with open('debug_search_response.html', 'w', encoding='utf-8') as f:
                f.write(search_response.text)
            print(f"💾 Saved search response to debug_search_response.html")
            
            # Try to parse search results
            print(f"\n📄 Parsing search results...")
            claims = navigator._parse_search_results(search_response, test_claim)
            print(f"🎯 Found {len(claims)} claim results")
            
            for i, claim in enumerate(claims):
                print(f"  Claim {i+1}:")
                print(f"    Number: {claim.get('claim_number', 'N/A')}")
                print(f"    URL: {claim.get('url', 'N/A')}")
                print(f"    Text: {claim.get('text', 'N/A')[:100]}...")
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            import traceback
            traceback.print_exc()
        
        manual_session.close()
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_updated_search()
