"""
Configuration management for XactAnalysis automation.

This module handles loading and managing configuration from various sources
including INI files, environment variables, and command line arguments.
"""

import os
import configparser
from pathlib import Path
from typing import Any, Dict, Optional
from dotenv import load_dotenv

from .exceptions import ConfigurationError


class ConfigManager:
    """Manages configuration for the XactAnalysis automation system."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file. If None, uses default.
        """
        self.project_root = Path(__file__).parent.parent.parent
        self.config_path = config_path or self.project_root / "config" / "config.ini"
        self.config = configparser.ConfigParser()
        
        # Load environment variables
        env_path = self.project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
        
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        if not self.config_path.exists():
            raise ConfigurationError(f"Configuration file not found: {self.config_path}")
        
        try:
            self.config.read(self.config_path)
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}")
    
    def get(self, section: str, key: str, fallback: Any = None) -> Any:
        """
        Get a configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Default value if key not found
            
        Returns:
            Configuration value
        """
        try:
            # Check environment variable first (format: SECTION_KEY)
            env_key = f"{section.upper()}_{key.upper()}"
            env_value = os.getenv(env_key)
            if env_value is not None:
                return self._convert_value(env_value)
            
            # Fall back to config file
            return self.config.get(section, key, fallback=fallback)
        except Exception as e:
            if fallback is not None:
                return fallback
            raise ConfigurationError(f"Configuration key not found: {section}.{key}")
    
    def get_int(self, section: str, key: str, fallback: Optional[int] = None) -> int:
        """Get an integer configuration value."""
        value = self.get(section, key, fallback)
        try:
            return int(value)
        except (ValueError, TypeError):
            if fallback is not None:
                return fallback
            raise ConfigurationError(f"Invalid integer value for {section}.{key}: {value}")
    
    def get_float(self, section: str, key: str, fallback: Optional[float] = None) -> float:
        """Get a float configuration value."""
        value = self.get(section, key, fallback)
        try:
            return float(value)
        except (ValueError, TypeError):
            if fallback is not None:
                return fallback
            raise ConfigurationError(f"Invalid float value for {section}.{key}: {value}")
    
    def get_bool(self, section: str, key: str, fallback: Optional[bool] = None) -> bool:
        """Get a boolean configuration value."""
        value = self.get(section, key, fallback)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', 'yes', '1', 'on')
        if fallback is not None:
            return fallback
        raise ConfigurationError(f"Invalid boolean value for {section}.{key}: {value}")
    
    def get_list(self, section: str, key: str, separator: str = ',', fallback: Optional[list] = None) -> list:
        """Get a list configuration value."""
        value = self.get(section, key, fallback)
        if value is None:
            return fallback or []
        if isinstance(value, list):
            return value
        return [item.strip() for item in str(value).split(separator) if item.strip()]
    
    def get_path(self, section: str, key: str, fallback: Optional[Path] = None) -> Path:
        """Get a path configuration value, resolved relative to project root."""
        value = self.get(section, key, fallback)
        if value is None:
            return fallback
        
        path = Path(value)
        if not path.is_absolute():
            path = self.project_root / path
        
        return path
    
    def _convert_value(self, value: str) -> Any:
        """Convert string value to appropriate type."""
        # Try boolean
        if value.lower() in ('true', 'false', 'yes', 'no', '1', '0', 'on', 'off'):
            return value.lower() in ('true', 'yes', '1', 'on')
        
        # Try integer
        try:
            return int(value)
        except ValueError:
            pass
        
        # Try float
        try:
            return float(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    @property
    def excel_file_path(self) -> Path:
        """Get the Excel file path."""
        return self.get_path('paths', 'excel_file')
    
    @property
    def output_dir(self) -> Path:
        """Get the output directory path."""
        return self.get_path('paths', 'output_dir')
    
    @property
    def log_dir(self) -> Path:
        """Get the log directory path."""
        return self.get_path('paths', 'log_dir')
    
    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        directories = [
            self.output_dir,
            self.log_dir,
            self.get_path('paths', 'screenshot_dir', Path('screenshots'))
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
