2025-06-16 20:57:31,512 - src.automation.browser_manager - INFO - Browser manager initialized
2025-06-16 20:57:31,512 - src.automation.browser_manager - INFO - Attempting to connect to existing browser session on port 9222
2025-06-16 20:57:32,156 - src.automation.browser_manager - INFO - Successfully connected to existing session. Current URL: https://www.xactanalysis.com/apps/xactanalysis/search.jsp?date_type=received&date_preset=365&xasp_status_type=in_progress&columns=cache
2025-06-16 20:57:42,747 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:57:42,762 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:09,213 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:09,226 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:47,286 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:47,290 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:49,810 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:58:49,814 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:59:10,937 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 20:59:10,941 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:03:55,459 - src.automation.browser_manager - INFO - Browser manager initialized
2025-06-16 21:03:55,460 - src.automation.browser_manager - INFO - Attempting to connect to existing browser session on port 9222
2025-06-16 21:03:56,066 - src.automation.browser_manager - INFO - Successfully connected to existing session. Current URL: https://www.xactanalysis.com/apps/xactanalysis/search.jsp?date_type=received&date_preset=365&xasp_status_type=in_progress&columns=cache
2025-06-16 21:04:02,928 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:02,928 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:02,929 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:02,935 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:02,935 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:02,935 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:10,186 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:10,186 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:10,187 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:10,190 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:10,191 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:10,191 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:37,687 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:37,687 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:37,687 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:37,691 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:37,692 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:37,692 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:39,644 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:39,645 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:39,645 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:39,649 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:04:39,649 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:04:39,650 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:14:03,807 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:14:03,807 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:14:03,808 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:14:03,812 - src.automation.browser_manager - ERROR - Failed to take screenshot: cannot switch to a different thread (which happens to have exited)
2025-06-16 21:14:03,812 - src.automation.browser_manager - WARNING - Browser connection lost, cleaning up
2025-06-16 21:14:03,813 - src.automation.browser_manager - WARNING - Error during cleanup: cannot switch to a different thread (which happens to have exited)
