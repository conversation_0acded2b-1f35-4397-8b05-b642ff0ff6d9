#!/usr/bin/env python3
"""
Debug script to analyze the Verisk login process.

This script helps understand the login flow for the actual XactAnalysis URL.
"""

import os
import sys
import requests
from pathlib import Path
from bs4 import BeautifulSoup

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))


def debug_login_flow():
    """Debug the Verisk login flow."""
    print("🔍 Debugging Verisk Login Flow")
    print("=" * 50)
    
    # Create session
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        # Step 1: Try the main XactAnalysis page
        print("📄 Step 1: Getting XactAnalysis main page...")
        main_url = "https://www.xactanalysis.com"
        main_response = session.get(main_url, timeout=30)
        print(f"   Status: {main_response.status_code}")
        print(f"   Final URL: {main_response.url}")
        
        # Step 2: Try the specific login URL you provided
        print("\n🔐 Step 2: Trying the specific login URL...")
        login_url = "https://identity.verisk.com/ui/login?returnUrl=%2Fconnect%2Fauthorize%2Fcallback%3Fui_locales%3Den-US%26scope%3Dopenid%2520claims.services.attachment.api%2520offline_access%2520xm8.services.internal.api%2520xm8.services.internal.administration%26response_type%3Dcode%2520id_token%26redirect_uri%3Dhttps%253A%252F%252Fwww.xactanalysis.com%252Fapps%252Frouting%252FrouteUser.do%26state%3D%257B%2522session_override%2522%253A%2522N%2522,%2522fast_route%2522%253A%2522%2522,%2522detail_tab%2522%253A%2522d_assignment%2522,%2522detail_mfn%2522%253A%2522%2522,%2522context%2522%253A%2522GENER%2522%257D%26nonce%3Dfw42AjJYNnCaIqosipCndw3rINpO3RUx%26client_id%3Dxactanalysis.hybrid%26response_mode%3Dform_post"
        
        login_response = session.get(login_url, timeout=30)
        print(f"   Status: {login_response.status_code}")
        print(f"   Final URL: {login_response.url}")
        
        # Step 3: Analyze the login page
        print("\n🔍 Step 3: Analyzing login page content...")
        soup = BeautifulSoup(login_response.content, 'html.parser')
        
        # Find forms
        forms = soup.find_all('form')
        print(f"   Found {len(forms)} form(s)")
        
        for i, form in enumerate(forms):
            print(f"\n   Form {i+1}:")
            print(f"     Action: {form.get('action', 'No action')}")
            print(f"     Method: {form.get('method', 'No method')}")
            
            # Find input fields
            inputs = form.find_all('input')
            print(f"     Inputs ({len(inputs)}):")
            for input_field in inputs:
                field_name = input_field.get('name', 'No name')
                field_type = input_field.get('type', 'No type')
                field_value = input_field.get('value', '')
                placeholder = input_field.get('placeholder', '')
                print(f"       - {field_name} ({field_type}): '{field_value}' placeholder='{placeholder}'")
        
        # Look for specific login indicators
        print("\n🔍 Step 4: Looking for login indicators...")
        
        # Check for username/email fields
        username_fields = soup.find_all('input', {'type': ['text', 'email']})
        print(f"   Username/email fields: {len(username_fields)}")
        for field in username_fields:
            print(f"     - {field.get('name', 'No name')} (placeholder: '{field.get('placeholder', '')}')")
        
        # Check for password fields
        password_fields = soup.find_all('input', {'type': 'password'})
        print(f"   Password fields: {len(password_fields)}")
        for field in password_fields:
            print(f"     - {field.get('name', 'No name')} (placeholder: '{field.get('placeholder', '')}')")
        
        # Check for submit buttons
        submit_buttons = soup.find_all(['input', 'button'], {'type': 'submit'})
        print(f"   Submit buttons: {len(submit_buttons)}")
        for button in submit_buttons:
            print(f"     - {button.get('name', 'No name')} (value: '{button.get('value', button.get_text())}')")
        
        # Step 5: Check page title and content
        print("\n📄 Step 5: Page analysis...")
        title = soup.find('title')
        print(f"   Page title: {title.get_text() if title else 'No title'}")
        
        # Look for error messages
        error_elements = soup.find_all(text=lambda text: text and 'error' in text.lower())
        if error_elements:
            print("   ⚠️  Potential error messages found:")
            for error in error_elements[:3]:  # Show first 3
                print(f"     - {error.strip()}")
        
        # Look for login-related text
        login_text = soup.find_all(text=lambda text: text and any(word in text.lower() 
                                   for word in ['login', 'sign in', 'username', 'password']))
        if login_text:
            print("   🔐 Login-related text found:")
            for text in login_text[:5]:  # Show first 5
                clean_text = text.strip()
                if clean_text:
                    print(f"     - {clean_text}")
        
        # Step 6: Save page content for manual inspection
        print("\n💾 Step 6: Saving page content...")
        with open('debug_login_page.html', 'w', encoding='utf-8') as f:
            f.write(login_response.text)
        print("   Saved login page to: debug_login_page.html")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main debug function."""
    try:
        success = debug_login_flow()
        if success:
            print("\n✅ Debug completed successfully!")
            print("💡 Check debug_login_page.html for the actual login page content")
        else:
            print("\n❌ Debug failed")
        return 0 if success else 1
    except Exception as e:
        print(f"\n❌ Debug script failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
