#!/usr/bin/env python3
"""
Setup script for XactAnalysis Beautiful Soup Automation.

This script helps users set up the automated system with proper configuration.
"""

import os
import sys
import shutil
from pathlib import Path
from getpass import getpass


def print_banner():
    """Print setup banner."""
    print("=" * 70)
    print("🛠️  XactAnalysis Automated Processor Setup")
    print("=" * 70)
    print()


def create_directories():
    """Create required directories."""
    directories = [
        'output',
        'logs', 
        'uploads',
        'config'
    ]
    
    print("📁 Creating directories...")
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ Created: {directory}")
        else:
            print(f"   ✓ Exists: {directory}")
    print()


def setup_environment_file():
    """Set up environment file with credentials."""
    env_file = Path('.env')
    env_example = Path('.env_bs4.example')
    
    print("🔐 Setting up credentials...")
    
    if env_file.exists():
        response = input("   .env file already exists. Overwrite? (y/N): ").strip().lower()
        if response != 'y':
            print("   Skipping credential setup.")
            return
    
    if not env_example.exists():
        print("   ❌ .env_bs4.example file not found!")
        return
    
    # Get credentials from user
    print("   Please enter your XactAnalysis credentials:")
    print("   (Press Enter to use test credentials: <EMAIL>)")
    username = input("   Username: ").strip()

    if not username:
        # Use test credentials
        username = "<EMAIL>"
        password = "Nubilt2025!"
        print("   ✅ Using test credentials")
    else:
        password = getpass("   Password: ").strip()

        if not password:
            print("   ❌ Password cannot be empty!")
            return
    
    # Copy example file and update with credentials
    try:
        with open(env_example, 'r') as f:
            content = f.read()
        
        # Replace placeholder values
        content = content.replace('your_username_here', username)
        content = content.replace('your_password_here', password)
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("   ✅ Credentials saved to .env file")
        print("   ⚠️  Keep this file secure and never commit it to version control!")
        
    except Exception as e:
        print(f"   ❌ Failed to create .env file: {e}")
    
    print()


def setup_configuration():
    """Set up configuration file."""
    config_file = Path('config/config_bs4.ini')
    
    print("⚙️  Setting up configuration...")
    
    if config_file.exists():
        print("   ✓ Configuration file already exists")
        return
    
    try:
        # Copy the default configuration
        default_config = Path('config/config_bs4.ini')
        if default_config.exists():
            print("   ✅ Configuration file ready")
        else:
            print("   ❌ Default configuration file not found!")
    except Exception as e:
        print(f"   ❌ Failed to set up configuration: {e}")
    
    print()


def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    
    requirements_file = Path('requirements.txt')
    
    if not requirements_file.exists():
        print("   ❌ requirements.txt not found!")
        return
    
    try:
        import subprocess
        
        # Check if we're in a virtual environment
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
        
        if not in_venv:
            print("   ⚠️  Not in a virtual environment!")
            response = input("   Continue anyway? (y/N): ").strip().lower()
            if response != 'y':
                print("   Skipping dependency installation.")
                print("   💡 Recommendation: Create a virtual environment first:")
                print("      python -m venv venv")
                print("      venv\\Scripts\\activate  (Windows)")
                print("      source venv/bin/activate  (Linux/Mac)")
                return
        
        print("   Installing packages...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ Dependencies installed successfully")
        else:
            print("   ❌ Failed to install dependencies:")
            print(f"      {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ Error installing dependencies: {e}")
    
    print()


def test_setup():
    """Test the setup."""
    print("🧪 Testing setup...")
    
    # Test imports
    try:
        sys.path.insert(0, str(Path(__file__).parent / 'src'))
        
        from src.automation_bs4.automation_controller import AutomationController
        from src.utils.config_manager import ConfigManager
        
        print("   ✅ Core modules import successfully")
        
        # Test configuration
        config = ConfigManager()
        print("   ✅ Configuration loads successfully")
        
        # Test credentials
        username = os.getenv('XACT_USERNAME')
        password = os.getenv('XACT_PASSWORD')
        
        if username and password:
            print("   ✅ Credentials found in environment")
        else:
            print("   ⚠️  Credentials not found in environment")
            print("      Make sure to set XACT_USERNAME and XACT_PASSWORD")
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        print("      Try installing dependencies first")
    except Exception as e:
        print(f"   ❌ Setup test failed: {e}")
    
    print()


def print_next_steps():
    """Print next steps for the user."""
    print("🎉 Setup Complete!")
    print()
    print("📋 Next Steps:")
    print("1. Make sure your credentials are set:")
    print("   - Check the .env file has your XactAnalysis username and password")
    print()
    print("2. Start the application:")
    print("   python run_bs4_app.py")
    print()
    print("3. Open your browser to:")
    print("   http://127.0.0.1:5001")
    print()
    print("4. Upload a CSV file with claim numbers and start processing!")
    print()
    print("💡 Tips:")
    print("   • CSV should have claim numbers in column B (index 1)")
    print("   • Completed claims will be marked with 'D' in column A")
    print("   • PDFs will be saved to the 'output' directory")
    print("   • Check 'logs' directory for detailed logs")
    print()


def main():
    """Main setup function."""
    print_banner()
    
    try:
        create_directories()
        setup_environment_file()
        setup_configuration()
        install_dependencies()
        test_setup()
        print_next_steps()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
