<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XactAnalysis Automated Processor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .activity-log {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
        }
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 2rem;
            text-align: center;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #0d6efd;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-dark bg-success">
                    <div class="container-fluid">
                        <span class="navbar-brand mb-0 h1">
                            <i class="fas fa-robot me-2"></i>
                            XactAnalysis Automated Processor
                        </span>
                        <div class="d-flex align-items-center">
                            <span id="login-status" class="badge bg-secondary me-2">
                                <span class="status-indicator status-disconnected"></span>
                                Not Logged In
                            </span>
                            <span id="current-time" class="text-light">{{ current_time }}</span>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row mt-4">
            <!-- Left Panel - Upload and Controls -->
            <div class="col-md-6">
                <!-- CSV Upload -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>Step 1: Upload CSV File
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h6>Drag & Drop CSV File Here</h6>
                            <p class="text-muted">or click to browse</p>
                            <input type="file" id="csv-file-input" accept=".csv" style="display: none;">
                            <button class="btn btn-outline-primary" onclick="document.getElementById('csv-file-input').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        <div id="file-info" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <strong>File loaded:</strong> <span id="file-name"></span><br>
                                <strong>Claims:</strong> <span id="file-claims"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Login -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-key me-2"></i>Step 2: Login to XactAnalysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            Make sure your credentials are set in environment variables:
                            <code>XACT_USERNAME</code> and <code>XACT_PASSWORD</code>
                        </p>
                        <button id="login-btn" class="btn btn-warning w-100" disabled>
                            <i class="fas fa-sign-in-alt me-2"></i>Login to XactAnalysis
                        </button>
                    </div>
                </div>

                <!-- Processing Controls -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-play me-2"></i>Step 3: Process Claims
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="single-claim" class="form-label">Process Single Claim:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="single-claim" placeholder="Enter claim number">
                                <button id="process-single-btn" class="btn btn-outline-success" disabled>
                                    <i class="fas fa-search me-1"></i>Process
                                </button>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button id="process-next-btn" class="btn btn-success" disabled>
                                <i class="fas fa-step-forward me-2"></i>Process Next Claim
                            </button>
                            <button id="process-all-btn" class="btn btn-danger" disabled>
                                <i class="fas fa-play-circle me-2"></i>Process All Claims Automatically
                            </button>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                The system will automatically search for claims, navigate to notes, 
                                and save PDFs with proper naming conventions.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Status and Progress -->
            <div class="col-md-6">
                <!-- Progress Overview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>Progress Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h3 id="total-claims" class="text-primary">0</h3>
                                <p class="text-muted">Total</p>
                            </div>
                            <div class="col-4">
                                <h3 id="completed-claims" class="text-success">0</h3>
                                <p class="text-muted">Completed</p>
                            </div>
                            <div class="col-4">
                                <h3 id="pending-claims" class="text-warning">0</h3>
                                <p class="text-muted">Pending</p>
                            </div>
                        </div>
                        <div class="progress">
                            <div id="progress-bar" class="progress-bar bg-success" role="progressbar" style="width: 0%">
                                <span id="progress-text">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Processing -->
                <div id="current-processing" class="card mb-4" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog fa-spin me-2"></i>Currently Processing
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Claim:</strong> <span id="current-claim">-</span><br>
                                <small class="text-muted">Status: <span id="current-status">-</span></small>
                            </div>
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">Processing...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Activity Log
                        </h5>
                        <button id="clear-log-btn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash me-1"></i>Clear
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="activity-log" class="activity-log">
                            <div class="log-entry text-muted">
                                <small><i class="fas fa-info-circle me-1"></i>Welcome! Upload a CSV file to get started.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global state
        let isLoggedIn = false;
        let csvLoaded = false;
        let isProcessing = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateStatus();
            updateTime();
            setInterval(updateTime, 1000);
        });

        function setupEventListeners() {
            // File upload
            const fileInput = document.getElementById('csv-file-input');
            const uploadArea = document.getElementById('upload-area');

            fileInput.addEventListener('change', handleFileSelect);
            
            // Drag and drop
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // Buttons
            document.getElementById('login-btn').addEventListener('click', handleLogin);
            document.getElementById('process-single-btn').addEventListener('click', handleProcessSingle);
            document.getElementById('process-next-btn').addEventListener('click', handleProcessNext);
            document.getElementById('process-all-btn').addEventListener('click', handleProcessAll);
            document.getElementById('clear-log-btn').addEventListener('click', clearLog);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                uploadFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        }

        function uploadFile(file) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                addLogEntry('error', 'Please select a CSV file');
                return;
            }

            const formData = new FormData();
            formData.append('csv_file', file);

            addLogEntry('info', `Uploading file: ${file.name}`);

            fetch('/api/upload_csv', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    csvLoaded = true;
                    updateFileInfo(data);
                    addLogEntry('success', `CSV file loaded successfully: ${data.summary.total_claims} claims found`);
                    updateButtons();
                } else {
                    addLogEntry('error', `Failed to load CSV: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Upload error: ${error.message}`);
            });
        }

        function updateFileInfo(data) {
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const fileClaims = document.getElementById('file-claims');

            fileName.textContent = data.file_path.split('/').pop();
            fileClaims.textContent = `${data.summary.total_claims} total, ${data.summary.completed_claims} completed, ${data.summary.pending_claims} pending`;
            
            fileInfo.style.display = 'block';
            updateProgress(data.summary);
        }

        function handleLogin() {
            if (isLoggedIn) return;

            addLogEntry('info', 'Attempting to login to XactAnalysis...');
            
            fetch('/api/login', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isLoggedIn = true;
                    updateLoginStatus(true);
                    addLogEntry('success', 'Successfully logged into XactAnalysis');
                    updateButtons();
                } else {
                    addLogEntry('error', `Login failed: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Login error: ${error.message}`);
            });
        }

        function handleProcessSingle() {
            const claimNumber = document.getElementById('single-claim').value.trim();
            if (!claimNumber) {
                addLogEntry('error', 'Please enter a claim number');
                return;
            }

            processClaim(claimNumber);
        }

        function handleProcessNext() {
            addLogEntry('info', 'Processing next pending claim...');
            
            fetch('/api/process_next', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.completed) {
                        addLogEntry('info', 'No more pending claims to process');
                    } else {
                        addLogEntry('success', `Successfully processed claim: ${data.claim_number}`);
                        updateStatus();
                    }
                } else {
                    addLogEntry('error', `Failed to process claim: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Process error: ${error.message}`);
            });
        }

        function handleProcessAll() {
            if (!confirm('This will process ALL pending claims automatically. Continue?')) {
                return;
            }

            isProcessing = true;
            updateButtons();
            showProcessingStatus('Processing all claims...', 'Starting batch processing');

            addLogEntry('info', 'Starting automatic processing of all claims...');
            
            fetch('/api/process_all', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                isProcessing = false;
                hideProcessingStatus();
                updateButtons();
                
                if (data.success) {
                    addLogEntry('success', `Batch processing completed: ${data.processed_count} successful, ${data.failed_count} failed`);
                    if (data.failed_count > 0) {
                        data.failed_claims.forEach(failed => {
                            addLogEntry('warning', `Failed claim ${failed.claim_number}: ${failed.error}`);
                        });
                    }
                    updateStatus();
                } else {
                    addLogEntry('error', `Batch processing failed: ${data.message}`);
                }
            })
            .catch(error => {
                isProcessing = false;
                hideProcessingStatus();
                updateButtons();
                addLogEntry('error', `Batch processing error: ${error.message}`);
            });
        }

        function processClaim(claimNumber) {
            showProcessingStatus(claimNumber, 'Searching for claim...');
            
            fetch('/api/process_claim', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ claim_number: claimNumber })
            })
            .then(response => response.json())
            .then(data => {
                hideProcessingStatus();
                
                if (data.success) {
                    addLogEntry('success', `Successfully processed claim ${claimNumber}: ${data.pdfs_generated} PDF(s) generated`);
                    updateStatus();
                } else {
                    addLogEntry('error', `Failed to process claim ${claimNumber}: ${data.message}`);
                }
            })
            .catch(error => {
                hideProcessingStatus();
                addLogEntry('error', `Process error: ${error.message}`);
            });
        }

        function updateStatus() {
            fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.status.csv_summary) {
                    updateProgress(data.status.csv_summary);
                }
            })
            .catch(error => {
                console.error('Status update error:', error);
            });
        }

        function updateProgress(summary) {
            document.getElementById('total-claims').textContent = summary.total_claims;
            document.getElementById('completed-claims').textContent = summary.completed_claims;
            document.getElementById('pending-claims').textContent = summary.pending_claims;
            
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const percent = summary.progress_percent;
            
            progressBar.style.width = `${percent}%`;
            progressText.textContent = `${percent}%`;
        }

        function updateLoginStatus(loggedIn) {
            const statusElement = document.getElementById('login-status');
            const indicator = statusElement.querySelector('.status-indicator');
            
            if (loggedIn) {
                statusElement.innerHTML = '<span class="status-indicator status-connected"></span>Logged In';
                statusElement.className = 'badge bg-success me-2';
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-disconnected"></span>Not Logged In';
                statusElement.className = 'badge bg-secondary me-2';
            }
        }

        function updateButtons() {
            document.getElementById('login-btn').disabled = !csvLoaded || isLoggedIn;
            document.getElementById('process-single-btn').disabled = !isLoggedIn || isProcessing;
            document.getElementById('process-next-btn').disabled = !isLoggedIn || isProcessing;
            document.getElementById('process-all-btn').disabled = !isLoggedIn || isProcessing;
        }

        function showProcessingStatus(claim, status) {
            document.getElementById('current-claim').textContent = claim;
            document.getElementById('current-status').textContent = status;
            document.getElementById('current-processing').style.display = 'block';
        }

        function hideProcessingStatus() {
            document.getElementById('current-processing').style.display = 'none';
        }

        function addLogEntry(type, message) {
            const log = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const timestamp = new Date().toLocaleTimeString();
            let icon, className;
            
            switch(type) {
                case 'success':
                    icon = 'fas fa-check-circle';
                    className = 'text-success';
                    break;
                case 'error':
                    icon = 'fas fa-exclamation-circle';
                    className = 'text-danger';
                    break;
                case 'warning':
                    icon = 'fas fa-exclamation-triangle';
                    className = 'text-warning';
                    break;
                default:
                    icon = 'fas fa-info-circle';
                    className = 'text-info';
            }
            
            entry.innerHTML = `<small class="${className}"><i class="${icon} me-1"></i>[${timestamp}] ${message}</small>`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
    </script>
</body>
</html>
