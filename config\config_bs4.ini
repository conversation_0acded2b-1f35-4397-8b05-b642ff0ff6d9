[DEFAULT]
# XactAnalysis Beautiful Soup Automation Configuration

[paths]
# Output directory for PDF files
output_dir = output
# Log directory
log_dir = logs
# Upload directory for CSV files
upload_dir = uploads

[csv]
# Column containing claim numbers (0-based index, B = 1)
claim_column = 1
# Column for completion status (0-based index, A = 0)
status_column = 0
# Status marker for completed claims
completion_marker = D

[xactanalysis]
# XactAnalysis website URL (Verisk Identity System)
base_url = https://identity.verisk.com
# Request timeout in seconds
timeout = 30
# Search delay in seconds (to avoid being too fast)
search_delay = 2
# Page navigation delay in seconds
nav_delay = 1
# Date range for searches (in years)
search_date_range_years = 3

[pdf]
# PDF file naming convention
# Available variables: {claim_number}, {suffix}
filename_template = {claim_number}{suffix}.pdf
# Suffix for multiple entries
multiple_suffixes = _A,_B,_C,_D,_E,_F,_G,_H,_I,_<PERSON>

[logging]
# Log level: DEBUG, INF<PERSON>, WARNING, ERROR, CRITICAL
log_level = INFO
# Log format
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
# Maximum log file size in MB
max_log_size = 10
# Number of backup log files to keep
backup_count = 5

[retry]
# Maximum number of retries for failed operations
max_retries = 3
# Base delay between retries in seconds
retry_delay = 5
# Exponential backoff multiplier
backoff_multiplier = 2

[security]
# Use environment variables for credentials (recommended)
use_env_credentials = true
# Credential storage method: env, keyring, prompt
credential_method = env

[web]
# Web application host
host = 127.0.0.1
# Web application port
port = 5001
# Debug mode (true/false)
debug = false
# Secret key for sessions (leave empty to auto-generate)
secret_key = 

[session]
# Session timeout in seconds
timeout = 3600
# Keep session alive between requests
keep_alive = true
# Maximum concurrent requests
max_concurrent_requests = 5

[performance]
# Delay between processing claims in seconds
claim_processing_delay = 1
# Maximum number of claims to process in one batch
max_batch_size = 100
# Enable parallel processing (experimental)
enable_parallel = false
