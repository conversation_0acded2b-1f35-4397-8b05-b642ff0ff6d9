#!/usr/bin/env python3
"""
Launch script for the Manual Login XactAnalysis automation.

This script starts the manual login version of the XactAnalysis processor.
"""

import os
import sys
import webbrowser
import threading
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.automation_bs4.manual_web_app import app
from src.utils.config_manager import ConfigManager


def print_banner():
    """Print application banner."""
    print("=" * 70)
    print("🔐 XactAnalysis Manual Login Processor")
    print("=" * 70)
    print()
    print("✨ Features:")
    print("  • Manual login for maximum reliability")
    print("  • Fast Beautiful Soup claim processing")
    print("  • Simple CSV upload interface")
    print("  • Automatic 3-year date range")
    print("  • Real-time progress tracking")
    print()


def check_environment():
    """Check if environment is properly configured."""
    issues = []
    
    # Check for required directories
    required_dirs = ['output', 'logs', 'uploads']
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created directory: {dir_name}")
    
    return issues


def print_instructions():
    """Print usage instructions."""
    print("📋 Manual Login Instructions:")
    print()
    print("🔄 Process Overview:")
    print("1. Upload your CSV file with claim numbers")
    print("2. Click 'Open XactAnalysis Login' to open browser")
    print("3. Login manually to XactAnalysis")
    print("4. Extract session cookies (instructions provided)")
    print("5. Process claims automatically with Beautiful Soup!")
    print()
    print("🎯 Benefits:")
    print("  • 100% reliable login (you do it manually)")
    print("  • Fast processing (Beautiful Soup)")
    print("  • No browser automation complexity")
    print("  • Works with any authentication method")
    print()


def open_browser_delayed(url, delay=2):
    """Open browser after a delay."""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 Opened browser: {url}")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print(f"   Please open manually: {url}")


def main():
    """Main entry point."""
    print_banner()
    
    # Check environment
    issues = check_environment()
    
    if issues:
        print("❌ Environment Issues Found:")
        for issue in issues:
            print(f"   • {issue}")
        print()
        return 1
    
    print("✅ Environment check passed!")
    print()
    
    print_instructions()
    
    try:
        # Load configuration
        config = ConfigManager()
        
        # Get web app settings
        host = config.get('web', 'host', '127.0.0.1')
        port = 5002  # Different port for manual version
        debug = config.get_bool('web', 'debug', False)
        
        url = f"http://{host}:{port}"
        
        print(f"🚀 Starting Manual Login XactAnalysis Processor...")
        print(f"   Host: {host}")
        print(f"   Port: {port}")
        print(f"   URL: {url}")
        print()
        print("Press Ctrl+C to stop the application")
        print("=" * 70)
        
        # Open browser in background
        browser_thread = threading.Thread(
            target=open_browser_delayed, 
            args=(url, 3),
            daemon=True
        )
        browser_thread.start()
        
        # Start the Flask app
        app.run(
            debug=debug,
            host=host,
            port=port,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
