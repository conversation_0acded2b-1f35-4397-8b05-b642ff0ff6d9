// Popup script for XactAnalysis automation extension

class ExtensionPopup {
    constructor() {
        this.isConnected = false;
        this.webAppUrl = 'http://127.0.0.1:5000';
        
        this.initializeElements();
        this.bindEvents();
        this.checkConnection();
    }
    
    initializeElements() {
        this.statusEl = document.getElementById('status');
        this.progressEl = document.getElementById('progress');
        this.progressTextEl = document.getElementById('progress-text');
        
        this.connectBtn = document.getElementById('connect-btn');
        this.claimInput = document.getElementById('claim-input');
        this.searchBtn = document.getElementById('search-btn');
        this.notesBtn = document.getElementById('notes-btn');
        this.pdfBtn = document.getElementById('pdf-btn');
        this.nextClaimBtn = document.getElementById('next-claim-btn');
    }
    
    bindEvents() {
        this.connectBtn.addEventListener('click', () => this.connectToWebApp());
        this.searchBtn.addEventListener('click', () => this.searchClaim());
        this.notesBtn.addEventListener('click', () => this.goToNotes());
        this.pdfBtn.addEventListener('click', () => this.generatePDF());
        this.nextClaimBtn.addEventListener('click', () => this.processNextClaim());
        
        this.claimInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchClaim();
            }
        });
    }
    
    async checkConnection() {
        try {
            const response = await fetch(`${this.webAppUrl}/api/status`);
            if (response.ok) {
                this.setConnected(true);
            } else {
                this.setConnected(false);
            }
        } catch (error) {
            this.setConnected(false);
        }
    }
    
    setConnected(connected) {
        this.isConnected = connected;
        
        if (connected) {
            this.statusEl.className = 'status connected';
            this.statusEl.textContent = '✅ Connected to Web App';
            this.connectBtn.textContent = '✅ Connected';
            this.connectBtn.disabled = true;
            
            // Enable controls
            this.claimInput.disabled = false;
            this.searchBtn.disabled = false;
            this.notesBtn.disabled = false;
            this.pdfBtn.disabled = false;
            this.nextClaimBtn.disabled = false;
        } else {
            this.statusEl.className = 'status disconnected';
            this.statusEl.textContent = '⚠️ Not Connected';
            this.connectBtn.textContent = '🔗 Connect to Web App';
            this.connectBtn.disabled = false;
            
            // Disable controls
            this.claimInput.disabled = true;
            this.searchBtn.disabled = true;
            this.notesBtn.disabled = true;
            this.pdfBtn.disabled = true;
            this.nextClaimBtn.disabled = true;
        }
    }
    
    showProgress(text) {
        this.progressTextEl.textContent = text;
        this.progressEl.style.display = 'block';
    }
    
    hideProgress() {
        this.progressEl.style.display = 'none';
    }
    
    async connectToWebApp() {
        this.showProgress('Connecting to web app...');
        
        try {
            const response = await fetch(`${this.webAppUrl}/api/status`);
            if (response.ok) {
                this.setConnected(true);
                
                // Register this extension with the web app
                await this.registerExtension();
            } else {
                throw new Error('Web app not responding');
            }
        } catch (error) {
            this.statusEl.className = 'status disconnected';
            this.statusEl.textContent = '❌ Connection Failed - Start Web App';
        } finally {
            this.hideProgress();
        }
    }
    
    async registerExtension() {
        try {
            // Get current tab info
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            await fetch(`${this.webAppUrl}/api/extension_connect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tabId: tab.id,
                    url: tab.url,
                    title: tab.title
                })
            });
        } catch (error) {
            console.error('Failed to register extension:', error);
        }
    }
    
    async searchClaim() {
        const claimNumber = this.claimInput.value.trim();
        if (!claimNumber) {
            alert('Please enter a claim number');
            return;
        }
        
        this.showProgress(`Searching for claim ${claimNumber}...`);
        
        try {
            // Send message to content script to search for claim
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'searchClaim',
                claimNumber: claimNumber
            });
            
            if (response && response.success) {
                this.statusEl.className = 'status connected';
                this.statusEl.textContent = `✅ Found claim ${claimNumber}`;
                
                // Notify web app
                await this.notifyWebApp('claim_found', { claimNumber });
            } else {
                this.statusEl.className = 'status disconnected';
                this.statusEl.textContent = `❌ Claim ${claimNumber} not found`;
            }
        } catch (error) {
            this.statusEl.className = 'status disconnected';
            this.statusEl.textContent = '❌ Search failed';
            console.error('Search error:', error);
        } finally {
            this.hideProgress();
        }
    }
    
    async goToNotes() {
        this.showProgress('Navigating to Notes...');
        
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'goToNotes'
            });
            
            if (response && response.success) {
                this.statusEl.className = 'status connected';
                this.statusEl.textContent = '✅ Navigated to Notes';
                
                // Notify web app
                await this.notifyWebApp('notes_opened');
            } else {
                this.statusEl.className = 'status disconnected';
                this.statusEl.textContent = '❌ Failed to navigate to Notes';
            }
        } catch (error) {
            this.statusEl.className = 'status disconnected';
            this.statusEl.textContent = '❌ Navigation failed';
            console.error('Navigation error:', error);
        } finally {
            this.hideProgress();
        }
    }
    
    async generatePDF() {
        this.showProgress('Generating PDF...');
        
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'generatePDF'
            });
            
            if (response && response.success) {
                this.statusEl.className = 'status connected';
                this.statusEl.textContent = '✅ PDF Generated';
                
                // Notify web app with PDF data
                await this.notifyWebApp('pdf_generated', response.data);
            } else {
                this.statusEl.className = 'status disconnected';
                this.statusEl.textContent = '❌ PDF generation failed';
            }
        } catch (error) {
            this.statusEl.className = 'status disconnected';
            this.statusEl.textContent = '❌ PDF generation failed';
            console.error('PDF generation error:', error);
        } finally {
            this.hideProgress();
        }
    }
    
    async processNextClaim() {
        this.showProgress('Getting next claim...');
        
        try {
            // Get next claim from web app
            const response = await fetch(`${this.webAppUrl}/api/get_next_claim`);
            const data = await response.json();
            
            if (data.success && data.claimNumber) {
                this.claimInput.value = data.claimNumber;
                
                // Automatically search for it
                await this.searchClaim();
            } else {
                this.statusEl.className = 'status waiting';
                this.statusEl.textContent = '✅ No more claims to process';
            }
        } catch (error) {
            this.statusEl.className = 'status disconnected';
            this.statusEl.textContent = '❌ Failed to get next claim';
            console.error('Next claim error:', error);
        } finally {
            this.hideProgress();
        }
    }
    
    async notifyWebApp(event, data = {}) {
        try {
            await fetch(`${this.webAppUrl}/api/extension_event`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: event,
                    data: data,
                    timestamp: new Date().toISOString()
                })
            });
        } catch (error) {
            console.error('Failed to notify web app:', error);
        }
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ExtensionPopup();
});
